"""
数据库连接管理模块
提供MySQL连接池和安全的数据库操作
"""

import asyncio
from typing import Any, Dict, List, Optional, Tuple
from contextlib import asynccontextmanager
import mysql.connector
from mysql.connector import pooling, Error
import pandas as pd
from loguru import logger

from config.settings import settings


class DatabaseManager:
    """数据库管理器，提供连接池和安全的数据库操作"""
    
    def __init__(self):
        self.pool: Optional[pooling.MySQLConnectionPool] = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化数据库连接池"""
        try:
            pool_config = {
                'pool_name': 'mysql_pool',
                'pool_size': settings.database.pool_size,
                'pool_reset_session': True,
                'host': settings.database.host,
                'port': settings.database.port,
                'user': settings.database.user,
                'password': settings.database.password,
                'database': settings.database.database,
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'autocommit': True,
                'time_zone': '+00:00'
            }
            
            self.pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("数据库连接池初始化成功")
            
        except Error as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接的异步上下文管理器"""
        connection = None
        try:
            connection = self.pool.get_connection()
            if connection.is_connected():
                yield connection
            else:
                raise Error("数据库连接失败")
        except Error as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    async def execute_query(
        self, 
        query: str, 
        params: Optional[Tuple] = None,
        fetch_results: bool = True
    ) -> Optional[List[Dict[str, Any]]]:
        """
        安全执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            fetch_results: 是否获取查询结果
            
        Returns:
            查询结果列表或None
        """
        async with self.get_connection() as connection:
            cursor = connection.cursor(dictionary=True)
            try:
                # 设置查询超时
                cursor.execute(f"SET SESSION max_execution_time = {settings.security.max_query_timeout * 1000}")
                
                # 执行查询
                cursor.execute(query, params or ())
                
                if fetch_results:
                    results = cursor.fetchall()
                    logger.info(f"查询执行成功，返回 {len(results)} 条记录")
                    return results
                else:
                    connection.commit()
                    logger.info(f"查询执行成功，影响 {cursor.rowcount} 行")
                    return None
                    
            except Error as e:
                logger.error(f"查询执行失败: {e}")
                connection.rollback()
                raise
            finally:
                cursor.close()
    
    async def execute_query_to_dataframe(
        self, 
        query: str, 
        params: Optional[Tuple] = None
    ) -> pd.DataFrame:
        """
        执行查询并返回pandas DataFrame
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果的DataFrame
        """
        results = await self.execute_query(query, params)
        if results:
            return pd.DataFrame(results)
        else:
            return pd.DataFrame()
    
    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            async with self.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                return result[0] == 1
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    async def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
        ORDER BY ORDINAL_POSITION
        """
        return await self.execute_query(query, (settings.database.database, table_name))
    
    async def get_all_tables(self) -> List[str]:
        """获取所有表名"""
        query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = %s AND TABLE_TYPE = 'BASE TABLE'
        """
        results = await self.execute_query(query, (settings.database.database,))
        return [row['TABLE_NAME'] for row in results] if results else []


# 全局数据库管理器实例
db_manager = DatabaseManager()
