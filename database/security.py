"""
数据库安全模块
提供SQL注入检测和查询安全验证
"""

import re
from typing import List, Tuple, Dict, Any
import sqlparse
from sqlparse import sql, tokens
from loguru import logger

from config.settings import settings


class SQLSecurityValidator:
    """SQL安全验证器"""
    
    # 危险的SQL关键词
    DANGEROUS_KEYWORDS = {
        'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'GRANT', 'REVOKE',
        'EXEC', 'EXECUTE', 'SHUTDOWN', 'KILL', 'LOAD_FILE', 'INTO OUTFILE',
        'INTO DUMPFILE', 'UNION', 'INFORMATION_SCHEMA', 'MYSQL', 'PERFORMANCE_SCHEMA'
    }
    
    # SQL注入模式
    INJECTION_PATTERNS = [
        r"(\b(OR|AND)\b\s+\d+\s*=\s*\d+)",  # OR 1=1, AND 1=1
        r"(\b(OR|AND)\b\s+['\"].*['\"])",    # OR 'x'='x'
        r"(;|\|\||&&)",                       # 语句分隔符
        r"(/\*.*?\*/)",                       # 注释
        r"(--.*$)",                           # 单行注释
        r"(\bUNION\b.*\bSELECT\b)",          # UNION SELECT
        r"(\bINTO\s+(OUTFILE|DUMPFILE)\b)",   # 文件操作
        r"(\bLOAD_FILE\b)",                   # 文件读取
        r"(\bEXEC\b|\bEXECUTE\b)",           # 执行命令
    ]
    
    def __init__(self):
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.INJECTION_PATTERNS]
    
    def validate_sql_operation(self, sql: str) -> Tuple[bool, str]:
        """
        验证SQL操作是否被允许
        
        Args:
            sql: SQL语句
            
        Returns:
            (是否允许, 错误信息)
        """
        try:
            parsed = sqlparse.parse(sql)[0]
            
            # 获取SQL语句类型
            statement_type = self._get_statement_type(parsed)
            
            if statement_type not in settings.security.allowed_operations:
                return False, f"不允许的SQL操作类型: {statement_type}"
            
            return True, ""
            
        except Exception as e:
            logger.error(f"SQL解析失败: {e}")
            return False, f"SQL解析失败: {str(e)}"
    
    def check_sql_injection(self, sql: str) -> Tuple[bool, List[str]]:
        """
        检查SQL注入攻击
        
        Args:
            sql: SQL语句
            
        Returns:
            (是否安全, 检测到的威胁列表)
        """
        if not settings.security.enable_sql_injection_check:
            return True, []
        
        threats = []
        
        # 检查危险关键词
        sql_upper = sql.upper()
        for keyword in self.DANGEROUS_KEYWORDS:
            if keyword in sql_upper:
                threats.append(f"检测到危险关键词: {keyword}")
        
        # 检查注入模式
        for pattern in self.compiled_patterns:
            matches = pattern.findall(sql)
            if matches:
                threats.append(f"检测到可疑模式: {matches[0]}")
        
        # 检查SQL语法
        try:
            parsed = sqlparse.parse(sql)
            if not parsed:
                threats.append("SQL语法解析失败")
        except Exception as e:
            threats.append(f"SQL语法错误: {str(e)}")
        
        is_safe = len(threats) == 0
        return is_safe, threats
    
    def sanitize_sql(self, sql: str) -> str:
        """
        清理SQL语句
        
        Args:
            sql: 原始SQL语句
            
        Returns:
            清理后的SQL语句
        """
        # 移除多余的空白字符
        sql = re.sub(r'\s+', ' ', sql.strip())
        
        # 移除注释
        sql = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
        sql = re.sub(r'/\*.*?\*/', '', sql, flags=re.DOTALL)
        
        return sql
    
    def _get_statement_type(self, parsed_sql) -> str:
        """获取SQL语句类型"""
        for token in parsed_sql.tokens:
            if token.ttype is tokens.Keyword.DML:
                return token.value.upper()
            elif token.ttype is tokens.Keyword.DDL:
                return token.value.upper()
            elif token.ttype is tokens.Keyword:
                return token.value.upper()
        return "UNKNOWN"
    
    def validate_query_complexity(self, sql: str) -> Tuple[bool, str]:
        """
        验证查询复杂度
        
        Args:
            sql: SQL语句
            
        Returns:
            (是否允许, 错误信息)
        """
        # 检查嵌套层级
        nesting_level = sql.count('(') - sql.count(')')
        if abs(nesting_level) > 5:
            return False, "查询嵌套层级过深"
        
        # 检查JOIN数量
        join_count = len(re.findall(r'\bJOIN\b', sql, re.IGNORECASE))
        if join_count > 10:
            return False, "JOIN操作过多"
        
        # 检查子查询数量
        subquery_count = len(re.findall(r'\bSELECT\b', sql, re.IGNORECASE)) - 1
        if subquery_count > 5:
            return False, "子查询过多"
        
        return True, ""


# 全局安全验证器实例
sql_validator = SQLSecurityValidator()
