"""
应用配置管理模块
使用Pydantic进行配置验证和管理
"""

from typing import List, Optional
from pydantic import BaseSettings, Field
from pydantic_settings import BaseSettings as PydanticBaseSettings


class DatabaseSettings(PydanticBaseSettings):
    """数据库配置"""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=3306, env="DB_PORT")
    user: str = Field(env="DB_USER")
    password: str = Field(env="DB_PASSWORD")
    database: str = Field(env="DB_NAME")
    
    # 连接池配置
    pool_size: int = Field(default=10, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    
    class Config:
        env_file = ".env"


class OpenAISettings(PydanticBaseSettings):
    """OpenAI API配置"""
    api_key: str = Field(env="OPENAI_API_KEY")
    model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    
    class Config:
        env_file = ".env"


class SecuritySettings(PydanticBaseSettings):
    """安全配置"""
    max_query_timeout: int = Field(default=30, env="MAX_QUERY_TIMEOUT")
    allowed_operations: List[str] = Field(
        default=["SELECT", "INSERT", "UPDATE", "DELETE"],
        env="ALLOWED_OPERATIONS"
    )
    enable_sql_injection_check: bool = Field(default=True, env="ENABLE_SQL_INJECTION_CHECK")
    
    class Config:
        env_file = ".env"


class LogSettings(PydanticBaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    file: str = Field(default="logs/app.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"


class AutoGenSettings(PydanticBaseSettings):
    """AutoGen配置"""
    cache_seed: int = Field(default=42, env="AUTOGEN_CACHE_SEED")
    timeout: int = Field(default=60, env="AUTOGEN_TIMEOUT")
    
    class Config:
        env_file = ".env"


class AppSettings(PydanticBaseSettings):
    """应用主配置"""
    database: DatabaseSettings = DatabaseSettings()
    openai: OpenAISettings = OpenAISettings()
    security: SecuritySettings = SecuritySettings()
    logging: LogSettings = LogSettings()
    autogen: AutoGenSettings = AutoGenSettings()
    
    class Config:
        env_file = ".env"


# 全局配置实例
settings = AppSettings()
