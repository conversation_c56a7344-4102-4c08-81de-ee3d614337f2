"""
错误处理工具模块
提供统一的错误处理和异常管理
"""

import traceback
from typing import Dict, Any, Optional, Type
from functools import wraps
from loguru import logger
from enum import Enum


class ErrorType(Enum):
    """错误类型枚举"""
    DATABASE_ERROR = "database_error"
    SQL_SYNTAX_ERROR = "sql_syntax_error"
    SECURITY_ERROR = "security_error"
    VALIDATION_ERROR = "validation_error"
    NETWORK_ERROR = "network_error"
    CONFIGURATION_ERROR = "configuration_error"
    AGENT_ERROR = "agent_error"
    UNKNOWN_ERROR = "unknown_error"


class SystemError(Exception):
    """系统自定义异常基类"""
    
    def __init__(
        self, 
        message: str, 
        error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.details = details or {}
        self.original_exception = original_exception
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.error_type.value,
            "message": self.message,
            "details": self.details,
            "original_error": str(self.original_exception) if self.original_exception else None
        }


class DatabaseError(SystemError):
    """数据库相关错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ErrorType.DATABASE_ERROR, details, original_exception)


class SQLSyntaxError(SystemError):
    """SQL语法错误"""
    
    def __init__(self, message: str, sql: str = "", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["sql"] = sql
        super().__init__(message, ErrorType.SQL_SYNTAX_ERROR, details)


class SecurityError(SystemError):
    """安全相关错误"""
    
    def __init__(self, message: str, threats: list = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["threats"] = threats or []
        super().__init__(message, ErrorType.SECURITY_ERROR, details)


class ValidationError(SystemError):
    """验证错误"""
    
    def __init__(self, message: str, field: str = "", value: Any = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details.update({"field": field, "value": value})
        super().__init__(message, ErrorType.VALIDATION_ERROR, details)


class AgentError(SystemError):
    """智能体错误"""
    
    def __init__(self, message: str, agent_name: str = "", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["agent_name"] = agent_name
        super().__init__(message, ErrorType.AGENT_ERROR, details)


class ErrorHandler:
    """错误处理器"""
    
    @staticmethod
    def handle_exception(
        exception: Exception, 
        context: str = "", 
        additional_info: Optional[Dict[str, Any]] = None
    ) -> SystemError:
        """
        处理异常并转换为系统错误
        
        Args:
            exception: 原始异常
            context: 错误上下文
            additional_info: 额外信息
            
        Returns:
            SystemError实例
        """
        additional_info = additional_info or {}
        
        # 如果已经是系统错误，直接返回
        if isinstance(exception, SystemError):
            return exception
        
        # 根据异常类型进行分类处理
        error_type = ErrorHandler._classify_exception(exception)
        
        # 构建错误消息
        message = f"{context}: {str(exception)}" if context else str(exception)
        
        # 创建详细信息
        details = {
            "exception_type": type(exception).__name__,
            "traceback": traceback.format_exc(),
            **additional_info
        }
        
        # 记录错误日志
        logger.error(f"错误处理 - {error_type.value}: {message}")
        logger.debug(f"错误详情: {details}")
        
        return SystemError(message, error_type, details, exception)
    
    @staticmethod
    def _classify_exception(exception: Exception) -> ErrorType:
        """分类异常类型"""
        exception_name = type(exception).__name__.lower()
        
        # 数据库相关错误
        if any(keyword in exception_name for keyword in ['mysql', 'database', 'connection', 'cursor']):
            return ErrorType.DATABASE_ERROR
        
        # SQL语法错误
        if any(keyword in exception_name for keyword in ['syntax', 'sql', 'parse']):
            return ErrorType.SQL_SYNTAX_ERROR
        
        # 网络相关错误
        if any(keyword in exception_name for keyword in ['network', 'timeout', 'connection']):
            return ErrorType.NETWORK_ERROR
        
        # 配置错误
        if any(keyword in exception_name for keyword in ['config', 'setting', 'environment']):
            return ErrorType.CONFIGURATION_ERROR
        
        # 验证错误
        if any(keyword in exception_name for keyword in ['validation', 'value', 'type']):
            return ErrorType.VALIDATION_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    @staticmethod
    def create_error_response(error: SystemError) -> Dict[str, Any]:
        """创建标准错误响应"""
        return {
            "success": False,
            "error": error.to_dict(),
            "timestamp": logger._core.now().isoformat(),
            "suggestions": ErrorHandler._get_error_suggestions(error)
        }
    
    @staticmethod
    def _get_error_suggestions(error: SystemError) -> list:
        """根据错误类型提供建议"""
        suggestions = []
        
        if error.error_type == ErrorType.DATABASE_ERROR:
            suggestions.extend([
                "检查数据库连接配置",
                "确认数据库服务是否运行",
                "验证用户权限设置"
            ])
        
        elif error.error_type == ErrorType.SQL_SYNTAX_ERROR:
            suggestions.extend([
                "检查SQL语法是否正确",
                "确认表名和字段名是否存在",
                "验证数据类型是否匹配"
            ])
        
        elif error.error_type == ErrorType.SECURITY_ERROR:
            suggestions.extend([
                "修改查询以符合安全要求",
                "避免使用危险的SQL操作",
                "确保查询参数安全"
            ])
        
        elif error.error_type == ErrorType.VALIDATION_ERROR:
            suggestions.extend([
                "检查输入参数格式",
                "确认必需字段已提供",
                "验证数据类型和范围"
            ])
        
        elif error.error_type == ErrorType.AGENT_ERROR:
            suggestions.extend([
                "检查智能体配置",
                "确认API密钥有效",
                "重试操作"
            ])
        
        else:
            suggestions.extend([
                "检查系统配置",
                "查看详细错误日志",
                "联系技术支持"
            ])
        
        return suggestions


def error_handler(context: str = ""):
    """错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error = ErrorHandler.handle_exception(e, context)
                raise error
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error = ErrorHandler.handle_exception(e, context)
                raise error
        
        # 根据函数类型返回相应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def safe_execute(func, default_value=None, context: str = ""):
    """安全执行函数，捕获异常并返回默认值"""
    try:
        return func()
    except Exception as e:
        error = ErrorHandler.handle_exception(e, context)
        logger.warning(f"安全执行失败，返回默认值: {error.message}")
        return default_value


# 全局错误处理器实例
error_handler_instance = ErrorHandler()
