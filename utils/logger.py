"""
日志配置模块
配置应用的日志记录
"""

import sys
import os
from pathlib import Path
from loguru import logger
from config.settings import settings


def setup_logger():
    """设置日志配置"""
    
    # 移除默认的logger
    logger.remove()
    
    # 创建日志目录
    log_file_path = Path(settings.logging.file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 控制台日志格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件日志格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=settings.logging.level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器
    logger.add(
        settings.logging.file,
        format=file_format,
        level=settings.logging.level,
        rotation="10 MB",
        retention="7 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误文件处理器
    error_log_file = log_file_path.parent / "error.log"
    logger.add(
        error_log_file,
        format=file_format,
        level="ERROR",
        rotation="5 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    logger.info("日志系统初始化完成")


def get_logger(name: str):
    """获取指定名称的logger"""
    return logger.bind(name=name)


# 初始化日志
setup_logger()
