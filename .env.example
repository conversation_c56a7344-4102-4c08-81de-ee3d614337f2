# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# MySQL数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_database_name

# 连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# 安全配置
MAX_QUERY_TIMEOUT=30
ALLOWED_OPERATIONS=SELECT,INSERT,UPDATE,DELETE
ENABLE_SQL_INJECTION_CHECK=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# AutoGen配置
AUTOGEN_CACHE_SEED=42
AUTOGEN_TIMEOUT=60
