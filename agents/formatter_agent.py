"""
结果格式化智能体
负责将查询结果格式化为用户友好的展示格式
"""

from typing import Dict, Any, List, Optional
import json
from tabulate import tabulate
from rich.console import Console
from rich.table import Table
from rich.text import Text
from io import StringIO

from agents.base_agent import BaseAgent, AgentMessage, MessageType
from loguru import logger


class FormatterAgent(BaseAgent):
    """结果格式化智能体，负责美化查询结果展示"""
    
    def __init__(self):
        super().__init__(
            name="结果格式化器",
            description="将查询结果格式化为用户友好的展示格式"
        )
        self.console = Console(file=StringIO(), width=120)
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理结果格式化请求"""
        try:
            if message.type != MessageType.RESULT:
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "格式化器只处理结果类型的消息"}
                )
            
            result_data = message.content
            
            # 检查执行是否成功
            if not result_data.get("success", False):
                return self._format_error_result(result_data)
            
            # 根据SQL类型格式化结果
            sql_type = result_data.get("sql_type", "UNKNOWN")
            
            if sql_type == "SELECT":
                formatted_result = self._format_select_result(result_data)
            else:
                formatted_result = self._format_modification_result(result_data)
            
            return self.create_response(
                MessageType.FORMAT,
                formatted_result,
                {
                    "original_result": result_data,
                    "formatting_type": sql_type
                }
            )
            
        except Exception as e:
            return self.log_error(e, "格式化结果时")
    
    def _format_select_result(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化SELECT查询结果"""
        data = result_data.get("data", [])
        columns = result_data.get("columns", [])
        row_count = result_data.get("row_count", 0)
        execution_time = result_data.get("execution_time_ms", 0)
        
        if not data:
            return {
                "formatted_output": "查询成功，但没有返回数据。",
                "summary": {
                    "row_count": 0,
                    "execution_time_ms": execution_time,
                    "message": "空结果集"
                },
                "formats": {
                    "table": "无数据",
                    "json": "[]",
                    "csv": ""
                }
            }
        
        # 生成多种格式
        formats = {
            "table": self._create_table_format(data, columns),
            "rich_table": self._create_rich_table_format(data, columns),
            "json": json.dumps(data, ensure_ascii=False, indent=2),
            "csv": self._create_csv_format(data, columns),
            "summary": self._create_summary_format(data, columns)
        }
        
        # 创建主要输出
        main_output = self._create_main_output(data, columns, row_count, execution_time)
        
        return {
            "formatted_output": main_output,
            "summary": {
                "row_count": row_count,
                "column_count": len(columns),
                "execution_time_ms": execution_time,
                "message": f"成功返回 {row_count} 行数据"
            },
            "formats": formats,
            "data_preview": data[:5] if len(data) > 5 else data  # 前5行预览
        }
    
    def _format_modification_result(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化修改操作结果"""
        message = result_data.get("message", "操作完成")
        execution_time = result_data.get("execution_time_ms", 0)
        sql_type = result_data.get("sql_type", "UNKNOWN")
        
        formatted_output = f"""
✅ {sql_type} 操作执行成功

📊 执行信息：
   • 操作类型: {sql_type}
   • 执行时间: {execution_time} ms
   • 状态: {message}
"""
        
        return {
            "formatted_output": formatted_output.strip(),
            "summary": {
                "operation_type": sql_type,
                "execution_time_ms": execution_time,
                "success": True,
                "message": message
            },
            "formats": {
                "simple": f"{sql_type} 操作成功完成",
                "detailed": formatted_output.strip()
            }
        }
    
    def _format_error_result(self, result_data: Dict[str, Any]) -> AgentMessage:
        """格式化错误结果"""
        error = result_data.get("error", "未知错误")
        error_type = result_data.get("error_type", "Error")
        sql = result_data.get("sql", "")
        
        formatted_output = f"""
❌ SQL执行失败

🔍 错误信息：
   • 错误类型: {error_type}
   • 错误描述: {error}
   • SQL语句: {sql[:100]}{'...' if len(sql) > 100 else ''}

💡 建议：
   • 检查SQL语法是否正确
   • 确认表名和字段名是否存在
   • 检查数据类型是否匹配
"""
        
        return self.create_response(
            MessageType.ERROR,
            {
                "formatted_output": formatted_output.strip(),
                "error_details": {
                    "error": error,
                    "error_type": error_type,
                    "sql": sql
                },
                "suggestions": [
                    "检查SQL语法",
                    "验证表名和字段名",
                    "确认数据类型匹配",
                    "检查权限设置"
                ]
            }
        )
    
    def _create_table_format(self, data: List[Dict], columns: List[str]) -> str:
        """创建表格格式"""
        if not data:
            return "无数据"
        
        # 准备表格数据
        table_data = []
        for row in data:
            table_data.append([str(row.get(col, "")) for col in columns])
        
        return tabulate(table_data, headers=columns, tablefmt="grid")
    
    def _create_rich_table_format(self, data: List[Dict], columns: List[str]) -> str:
        """创建Rich表格格式"""
        if not data:
            return "无数据"
        
        # 重置console
        self.console.file = StringIO()
        
        # 创建Rich表格
        table = Table(show_header=True, header_style="bold magenta")
        
        # 添加列
        for col in columns:
            table.add_column(col, style="cyan")
        
        # 添加行（限制显示行数）
        display_rows = data[:20]  # 最多显示20行
        for row in display_rows:
            table.add_row(*[str(row.get(col, "")) for col in columns])
        
        if len(data) > 20:
            table.add_row(*["..." for _ in columns])
        
        self.console.print(table)
        output = self.console.file.getvalue()
        
        return output
    
    def _create_csv_format(self, data: List[Dict], columns: List[str]) -> str:
        """创建CSV格式"""
        if not data:
            return ""
        
        lines = [",".join(columns)]
        for row in data:
            csv_row = []
            for col in columns:
                value = str(row.get(col, ""))
                # 处理包含逗号的值
                if "," in value or '"' in value:
                    value = f'"{value.replace('"', '""')}"'
                csv_row.append(value)
            lines.append(",".join(csv_row))
        
        return "\n".join(lines)
    
    def _create_summary_format(self, data: List[Dict], columns: List[str]) -> str:
        """创建摘要格式"""
        if not data:
            return "无数据摘要"
        
        summary = f"数据摘要:\n"
        summary += f"• 总行数: {len(data)}\n"
        summary += f"• 总列数: {len(columns)}\n"
        summary += f"• 列名: {', '.join(columns)}\n"
        
        # 数值列统计
        numeric_stats = self._calculate_numeric_stats(data, columns)
        if numeric_stats:
            summary += f"• 数值统计:\n"
            for col, stats in numeric_stats.items():
                summary += f"  - {col}: 最小值={stats['min']}, 最大值={stats['max']}, 平均值={stats['avg']:.2f}\n"
        
        return summary
    
    def _create_main_output(
        self, 
        data: List[Dict], 
        columns: List[str], 
        row_count: int, 
        execution_time: float
    ) -> str:
        """创建主要输出格式"""
        output = f"✅ 查询执行成功\n\n"
        output += f"📊 结果统计:\n"
        output += f"   • 返回行数: {row_count}\n"
        output += f"   • 列数: {len(columns)}\n"
        output += f"   • 执行时间: {execution_time} ms\n\n"
        
        # 添加表格（限制显示行数）
        if data:
            display_count = min(10, len(data))
            output += f"📋 数据预览 (前 {display_count} 行):\n\n"
            
            preview_data = data[:display_count]
            table_str = self._create_table_format(preview_data, columns)
            output += table_str
            
            if len(data) > display_count:
                output += f"\n\n... 还有 {len(data) - display_count} 行数据未显示"
        
        return output
    
    def _calculate_numeric_stats(self, data: List[Dict], columns: List[str]) -> Dict[str, Dict]:
        """计算数值列的统计信息"""
        stats = {}
        
        for col in columns:
            values = []
            for row in data:
                try:
                    value = float(row.get(col, 0))
                    values.append(value)
                except (ValueError, TypeError):
                    continue
            
            if values:
                stats[col] = {
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values)
                }
        
        return stats


# 全局格式化智能体实例
formatter_agent = FormatterAgent()
