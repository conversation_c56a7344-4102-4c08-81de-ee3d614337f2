"""
协调者智能体
负责管理整个自然语言到SQL的工作流程
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

from agents.base_agent import BaseAgent, AgentMessage, MessageType
from agents.sql_expert_agent import sql_expert
from agents.security_agent import security_agent
from agents.executor_agent import executor_agent
from agents.formatter_agent import formatter_agent
from loguru import logger


class CoordinatorAgent(BaseAgent):
    """协调者智能体，管理整个查询处理流程"""
    
    def __init__(self):
        super().__init__(
            name="流程协调者",
            description="协调各个智能体完成自然语言到SQL的完整流程"
        )
        
        # 工作流程步骤
        self.workflow_steps = [
            ("sql_generation", sql_expert),
            ("security_validation", security_agent),
            ("sql_execution", executor_agent),
            ("result_formatting", formatter_agent)
        ]
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理完整的查询流程"""
        try:
            if message.type != MessageType.QUERY:
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "协调者只处理查询类型的消息"}
                )
            
            query = message.content
            session_id = message.metadata.get("session_id", "default") if message.metadata else "default"
            
            logger.info(f"开始处理查询: {query[:100]}...")
            
            # 执行完整工作流程
            workflow_result = await self._execute_workflow(query, session_id)
            
            return self.create_response(
                MessageType.FORMAT,
                workflow_result,
                {
                    "original_query": query,
                    "session_id": session_id,
                    "workflow_completed": True
                }
            )
            
        except Exception as e:
            return self.log_error(e, "执行工作流程时")
    
    async def _execute_workflow(self, query: str, session_id: str) -> Dict[str, Any]:
        """执行完整的工作流程"""
        workflow_result = {
            "query": query,
            "session_id": session_id,
            "start_time": datetime.now().isoformat(),
            "steps": {},
            "final_result": None,
            "success": False,
            "error": None
        }
        
        current_message = AgentMessage(
            type=MessageType.QUERY,
            content=query,
            metadata={"session_id": session_id},
            sender="user"
        )
        
        try:
            # 逐步执行工作流程
            for step_name, agent in self.workflow_steps:
                logger.info(f"执行步骤: {step_name} - {agent.name}")
                
                step_start_time = datetime.now()
                
                # 执行当前步骤
                step_result = await agent.process_message(current_message)
                
                step_end_time = datetime.now()
                step_duration = (step_end_time - step_start_time).total_seconds() * 1000
                
                # 记录步骤结果
                workflow_result["steps"][step_name] = {
                    "agent": agent.name,
                    "start_time": step_start_time.isoformat(),
                    "end_time": step_end_time.isoformat(),
                    "duration_ms": round(step_duration, 2),
                    "success": step_result.type != MessageType.ERROR,
                    "result": step_result.content,
                    "message_type": step_result.type.value
                }
                
                # 检查步骤是否成功
                if step_result.type == MessageType.ERROR:
                    workflow_result["error"] = step_result.content
                    workflow_result["failed_step"] = step_name
                    logger.error(f"工作流程在步骤 {step_name} 失败: {step_result.content}")
                    break
                
                # 准备下一步的输入
                current_message = step_result
            
            else:
                # 所有步骤都成功完成
                workflow_result["success"] = True
                workflow_result["final_result"] = current_message.content
                logger.info("工作流程成功完成")
            
            workflow_result["end_time"] = datetime.now().isoformat()
            
            # 计算总耗时
            start_dt = datetime.fromisoformat(workflow_result["start_time"])
            end_dt = datetime.fromisoformat(workflow_result["end_time"])
            total_duration = (end_dt - start_dt).total_seconds() * 1000
            workflow_result["total_duration_ms"] = round(total_duration, 2)
            
            return workflow_result
            
        except Exception as e:
            workflow_result["error"] = str(e)
            workflow_result["end_time"] = datetime.now().isoformat()
            logger.error(f"工作流程执行异常: {e}")
            return workflow_result
    
    async def process_batch_queries(self, queries: List[str], session_id: str = "batch") -> List[Dict[str, Any]]:
        """批量处理查询"""
        results = []
        
        logger.info(f"开始批量处理 {len(queries)} 个查询")
        
        for i, query in enumerate(queries):
            logger.info(f"处理第 {i+1}/{len(queries)} 个查询")
            
            message = AgentMessage(
                type=MessageType.QUERY,
                content=query,
                metadata={"session_id": f"{session_id}_{i}", "batch_index": i},
                sender="batch_user"
            )
            
            try:
                result = await self.process_message(message)
                results.append(result.content)
            except Exception as e:
                logger.error(f"批量查询第 {i+1} 个失败: {e}")
                results.append({
                    "query": query,
                    "success": False,
                    "error": str(e),
                    "batch_index": i
                })
        
        logger.info(f"批量处理完成，成功: {sum(1 for r in results if r.get('success', False))}/{len(queries)}")
        
        return results
    
    async def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流程状态"""
        try:
            # 获取各个智能体的状态
            agent_statuses = {}
            
            for step_name, agent in self.workflow_steps:
                if hasattr(agent, 'get_execution_stats'):
                    agent_statuses[step_name] = await agent.get_execution_stats()
                else:
                    agent_statuses[step_name] = {
                        "name": agent.name,
                        "message_count": len(agent.message_history)
                    }
            
            return {
                "coordinator_status": "active",
                "total_workflows": len(self.message_history),
                "agent_statuses": agent_statuses,
                "workflow_steps": [step[0] for step in self.workflow_steps]
            }
            
        except Exception as e:
            logger.error(f"获取工作流程状态失败: {e}")
            return {
                "coordinator_status": "error",
                "error": str(e)
            }
    
    def get_workflow_summary(self, workflow_result: Dict[str, Any]) -> str:
        """生成工作流程摘要"""
        if not workflow_result.get("success", False):
            return f"""
❌ 查询处理失败

🔍 查询内容: {workflow_result.get('query', 'N/A')[:100]}...
❌ 失败步骤: {workflow_result.get('failed_step', 'unknown')}
⚠️  错误信息: {workflow_result.get('error', {}).get('error', 'Unknown error')}
⏱️  总耗时: {workflow_result.get('total_duration_ms', 0)} ms
"""
        
        final_result = workflow_result.get("final_result", {})
        summary = final_result.get("summary", {})
        
        return f"""
✅ 查询处理成功

🔍 查询内容: {workflow_result.get('query', 'N/A')[:100]}...
📊 结果统计: {summary.get('message', '处理完成')}
⏱️  总耗时: {workflow_result.get('total_duration_ms', 0)} ms

各步骤耗时:
{self._format_step_durations(workflow_result.get('steps', {}))}
"""
    
    def _format_step_durations(self, steps: Dict[str, Any]) -> str:
        """格式化步骤耗时信息"""
        lines = []
        for step_name, step_info in steps.items():
            duration = step_info.get('duration_ms', 0)
            success = "✅" if step_info.get('success', False) else "❌"
            lines.append(f"  {success} {step_info.get('agent', step_name)}: {duration} ms")
        
        return "\n".join(lines)


# 全局协调者智能体实例
coordinator = CoordinatorAgent()
