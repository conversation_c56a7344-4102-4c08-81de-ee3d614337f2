"""
数据库执行智能体
负责执行经过安全验证的SQL语句
"""

import asyncio
from typing import Dict, Any, List, Optional
import pandas as pd
from agents.base_agent import BaseAgent, AgentMessage, MessageType
from database.connection import db_manager
from loguru import logger


class ExecutorAgent(BaseAgent):
    """数据库执行智能体，负责安全执行SQL语句"""
    
    def __init__(self):
        super().__init__(
            name="数据库执行器",
            description="安全执行经过验证的SQL语句并返回结果"
        )
        self.max_result_rows = 1000  # 最大返回行数限制
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理SQL执行请求"""
        try:
            if message.type != MessageType.VALIDATION:
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "执行器只处理经过验证的SQL消息"}
                )
            
            validation_data = message.content
            
            # 检查验证状态
            if validation_data.get("status") != "approved":
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "SQL未通过安全验证，拒绝执行"}
                )
            
            sql_statement = validation_data.get("sql")
            if not sql_statement:
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "缺少SQL语句"}
                )
            
            # 执行SQL语句
            execution_result = await self._execute_sql_safely(sql_statement)
            
            return self.create_response(
                MessageType.RESULT,
                execution_result,
                {
                    "original_sql": sql_statement,
                    "security_checks": validation_data.get("security_checks", {})
                }
            )
            
        except Exception as e:
            return self.log_error(e, "执行SQL语句时")
    
    async def _execute_sql_safely(self, sql: str) -> Dict[str, Any]:
        """安全执行SQL语句"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            # 判断SQL类型
            sql_type = self._get_sql_type(sql)
            
            if sql_type == "SELECT":
                result = await self._execute_select_query(sql)
            else:
                result = await self._execute_modification_query(sql)
            
            end_time = asyncio.get_event_loop().time()
            execution_time = round((end_time - start_time) * 1000, 2)  # 毫秒
            
            result["execution_time_ms"] = execution_time
            result["sql_type"] = sql_type
            
            logger.info(f"SQL执行成功: {sql_type}, 耗时: {execution_time}ms")
            
            return result
            
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": sql,
                "error_type": type(e).__name__
            }
    
    async def _execute_select_query(self, sql: str) -> Dict[str, Any]:
        """执行SELECT查询"""
        try:
            # 添加行数限制（如果没有LIMIT子句）
            if "LIMIT" not in sql.upper():
                sql = f"{sql.rstrip(';')} LIMIT {self.max_result_rows}"
            
            # 执行查询并获取DataFrame
            df = await db_manager.execute_query_to_dataframe(sql)
            
            # 处理结果
            if df.empty:
                return {
                    "success": True,
                    "data": [],
                    "columns": [],
                    "row_count": 0,
                    "message": "查询成功，但没有返回数据"
                }
            
            # 转换数据类型以确保JSON序列化
            df_serializable = self._make_dataframe_serializable(df)
            
            return {
                "success": True,
                "data": df_serializable.to_dict('records'),
                "columns": df_serializable.columns.tolist(),
                "row_count": len(df_serializable),
                "message": f"查询成功，返回 {len(df_serializable)} 行数据"
            }
            
        except Exception as e:
            raise Exception(f"SELECT查询执行失败: {str(e)}")
    
    async def _execute_modification_query(self, sql: str) -> Dict[str, Any]:
        """执行修改类查询（INSERT, UPDATE, DELETE）"""
        try:
            # 执行修改操作
            await db_manager.execute_query(sql, fetch_results=False)
            
            return {
                "success": True,
                "message": "操作执行成功",
                "affected_rows": "操作完成"  # MySQL connector不总是返回准确的affected_rows
            }
            
        except Exception as e:
            raise Exception(f"修改操作执行失败: {str(e)}")
    
    def _get_sql_type(self, sql: str) -> str:
        """获取SQL语句类型"""
        sql_upper = sql.strip().upper()
        
        if sql_upper.startswith("SELECT"):
            return "SELECT"
        elif sql_upper.startswith("INSERT"):
            return "INSERT"
        elif sql_upper.startswith("UPDATE"):
            return "UPDATE"
        elif sql_upper.startswith("DELETE"):
            return "DELETE"
        else:
            return "UNKNOWN"
    
    def _make_dataframe_serializable(self, df: pd.DataFrame) -> pd.DataFrame:
        """使DataFrame可序列化为JSON"""
        df_copy = df.copy()
        
        # 处理各种数据类型
        for column in df_copy.columns:
            dtype = df_copy[column].dtype
            
            # 处理日期时间类型
            if pd.api.types.is_datetime64_any_dtype(dtype):
                df_copy[column] = df_copy[column].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # 处理NaN值
            elif df_copy[column].isna().any():
                if pd.api.types.is_numeric_dtype(dtype):
                    df_copy[column] = df_copy[column].fillna(0)
                else:
                    df_copy[column] = df_copy[column].fillna("")
            
            # 处理Decimal类型
            elif dtype == 'object':
                try:
                    # 尝试转换为字符串
                    df_copy[column] = df_copy[column].astype(str)
                except:
                    pass
        
        return df_copy
    
    async def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        try:
            # 测试数据库连接
            is_connected = await db_manager.test_connection()
            
            return {
                "database_connected": is_connected,
                "max_result_rows": self.max_result_rows,
                "total_executions": len(self.message_history),
                "successful_executions": len([
                    msg for msg in self.message_history 
                    if msg.type == MessageType.RESULT and 
                    isinstance(msg.content, dict) and 
                    msg.content.get("success", False)
                ])
            }
            
        except Exception as e:
            logger.error(f"获取执行统计失败: {e}")
            return {
                "error": str(e),
                "database_connected": False
            }


# 全局执行器智能体实例
executor_agent = ExecutorAgent()
