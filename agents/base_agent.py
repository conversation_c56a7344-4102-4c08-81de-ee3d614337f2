"""
基础智能体类
定义所有智能体的通用接口和功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import json
from loguru import logger


class MessageType(Enum):
    """消息类型枚举"""
    QUERY = "query"
    SQL = "sql"
    RESULT = "result"
    ERROR = "error"
    VALIDATION = "validation"
    FORMAT = "format"


@dataclass
class AgentMessage:
    """智能体消息数据结构"""
    type: MessageType
    content: Any
    metadata: Optional[Dict[str, Any]] = None
    sender: Optional[str] = None
    timestamp: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "type": self.type.value,
            "content": self.content,
            "metadata": self.metadata or {},
            "sender": self.sender,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentMessage':
        """从字典创建消息对象"""
        return cls(
            type=MessageType(data["type"]),
            content=data["content"],
            metadata=data.get("metadata"),
            sender=data.get("sender"),
            timestamp=data.get("timestamp")
        )


class BaseAgent(ABC):
    """基础智能体抽象类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.message_history: List[AgentMessage] = []
        
    @abstractmethod
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """
        处理接收到的消息
        
        Args:
            message: 输入消息
            
        Returns:
            处理后的输出消息
        """
        pass
    
    def add_to_history(self, message: AgentMessage):
        """添加消息到历史记录"""
        self.message_history.append(message)
        logger.debug(f"{self.name} 添加消息到历史: {message.type.value}")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return f"""
你是一个专业的{self.name}智能体。
职责：{self.description}

请严格按照你的职责范围工作，确保输出的准确性和安全性。
所有的响应都应该是结构化的JSON格式。
"""
    
    def create_response(
        self, 
        message_type: MessageType, 
        content: Any, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> AgentMessage:
        """创建响应消息"""
        response = AgentMessage(
            type=message_type,
            content=content,
            metadata=metadata,
            sender=self.name
        )
        self.add_to_history(response)
        return response
    
    def log_error(self, error: Exception, context: str = ""):
        """记录错误日志"""
        error_msg = f"{self.name} 错误 {context}: {str(error)}"
        logger.error(error_msg)
        return self.create_response(
            MessageType.ERROR,
            {"error": str(error), "context": context}
        )
