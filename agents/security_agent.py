"""
安全审查智能体
负责SQL语句的安全检查和验证
"""

from typing import Dict, Any, List, Tuple
from agents.base_agent import BaseAgent, AgentMessage, MessageType
from database.security import sql_validator
from loguru import logger


class SecurityAgent(BaseAgent):
    """安全审查智能体，负责SQL安全检查"""
    
    def __init__(self):
        super().__init__(
            name="安全审查员",
            description="检查SQL语句的安全性，防止SQL注入和恶意操作"
        )
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理SQL安全检查请求"""
        try:
            if message.type != MessageType.SQL:
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "安全审查员只处理SQL类型的消息"}
                )
            
            sql_data = message.content
            
            # 检查消息格式
            if isinstance(sql_data, dict) and "sql" in sql_data:
                sql_statement = sql_data["sql"]
            elif isinstance(sql_data, str):
                sql_statement = sql_data
            else:
                return self.create_response(
                    MessageType.ERROR,
                    {"error": "无效的SQL数据格式"}
                )
            
            # 执行安全检查
            security_result = await self._comprehensive_security_check(sql_statement)
            
            if security_result["is_safe"]:
                return self.create_response(
                    MessageType.VALIDATION,
                    {
                        "status": "approved",
                        "sql": sql_statement,
                        "original_data": sql_data,
                        "security_checks": security_result
                    }
                )
            else:
                return self.create_response(
                    MessageType.ERROR,
                    {
                        "status": "rejected",
                        "sql": sql_statement,
                        "security_issues": security_result["threats"],
                        "recommendations": security_result.get("recommendations", [])
                    }
                )
                
        except Exception as e:
            return self.log_error(e, "执行安全检查时")
    
    async def _comprehensive_security_check(self, sql: str) -> Dict[str, Any]:
        """执行全面的安全检查"""
        result = {
            "is_safe": True,
            "threats": [],
            "warnings": [],
            "recommendations": []
        }
        
        try:
            # 1. SQL操作类型验证
            is_allowed, operation_error = sql_validator.validate_sql_operation(sql)
            if not is_allowed:
                result["is_safe"] = False
                result["threats"].append(f"操作类型检查失败: {operation_error}")
            
            # 2. SQL注入检查
            is_injection_safe, injection_threats = sql_validator.check_sql_injection(sql)
            if not is_injection_safe:
                result["is_safe"] = False
                result["threats"].extend(injection_threats)
            
            # 3. 查询复杂度检查
            is_complexity_ok, complexity_error = sql_validator.validate_query_complexity(sql)
            if not is_complexity_ok:
                result["is_safe"] = False
                result["threats"].append(f"查询复杂度检查失败: {complexity_error}")
            
            # 4. 敏感操作检查
            sensitive_warnings = self._check_sensitive_operations(sql)
            if sensitive_warnings:
                result["warnings"].extend(sensitive_warnings)
            
            # 5. 性能风险检查
            performance_warnings = self._check_performance_risks(sql)
            if performance_warnings:
                result["warnings"].extend(performance_warnings)
            
            # 6. 生成安全建议
            result["recommendations"] = self._generate_security_recommendations(sql, result)
            
            logger.info(f"安全检查完成: {'通过' if result['is_safe'] else '失败'}")
            
        except Exception as e:
            result["is_safe"] = False
            result["threats"].append(f"安全检查过程中发生错误: {str(e)}")
            logger.error(f"安全检查异常: {e}")
        
        return result
    
    def _check_sensitive_operations(self, sql: str) -> List[str]:
        """检查敏感操作"""
        warnings = []
        sql_upper = sql.upper()
        
        # 检查可能的敏感操作
        sensitive_patterns = {
            "DELETE": "检测到DELETE操作，请确认是否需要WHERE条件",
            "UPDATE": "检测到UPDATE操作，请确认是否需要WHERE条件",
            "TRUNCATE": "检测到TRUNCATE操作，这将清空整个表",
            "DROP": "检测到DROP操作，这将删除数据库对象",
            "ALTER": "检测到ALTER操作，这将修改表结构"
        }
        
        for pattern, warning in sensitive_patterns.items():
            if pattern in sql_upper:
                warnings.append(warning)
        
        # 检查没有WHERE条件的UPDATE/DELETE
        if ("UPDATE" in sql_upper or "DELETE" in sql_upper) and "WHERE" not in sql_upper:
            warnings.append("UPDATE/DELETE操作缺少WHERE条件，可能影响所有记录")
        
        return warnings
    
    def _check_performance_risks(self, sql: str) -> List[str]:
        """检查性能风险"""
        warnings = []
        sql_upper = sql.upper()
        
        # 检查可能的性能问题
        if "SELECT *" in sql_upper:
            warnings.append("使用SELECT *可能影响性能，建议指定具体字段")
        
        if sql_upper.count("JOIN") > 5:
            warnings.append("过多的JOIN操作可能影响查询性能")
        
        if "ORDER BY" in sql_upper and "LIMIT" not in sql_upper:
            warnings.append("ORDER BY操作建议配合LIMIT使用以提高性能")
        
        if "LIKE" in sql_upper and sql.count("'%") > 0:
            warnings.append("前置通配符的LIKE查询可能无法使用索引")
        
        return warnings
    
    def _generate_security_recommendations(self, sql: str, check_result: Dict[str, Any]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        if not check_result["is_safe"]:
            recommendations.append("请修复所有安全威胁后重新提交")
        
        if check_result["warnings"]:
            recommendations.append("请注意性能和安全警告")
        
        # 基于SQL内容的建议
        sql_upper = sql.upper()
        
        if "WHERE" not in sql_upper and any(op in sql_upper for op in ["UPDATE", "DELETE"]):
            recommendations.append("建议为UPDATE/DELETE操作添加WHERE条件")
        
        if "LIMIT" not in sql_upper and "SELECT" in sql_upper:
            recommendations.append("建议为SELECT查询添加LIMIT限制返回结果数量")
        
        if not recommendations:
            recommendations.append("SQL语句通过安全检查")
        
        return recommendations


# 全局安全审查智能体实例
security_agent = SecurityAgent()
