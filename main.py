"""
AI智能体文本转SQL系统主程序
基于AutoGen 0.6框架的多智能体系统
"""

import asyncio
import sys
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils.logger import setup_logger, get_logger
from config.settings import settings
from agents.coordinator_agent import coordinator
from agents.base_agent import AgentMessage, MessageType
from database.connection import db_manager

# 设置日志
logger = get_logger(__name__)


class TextToSQLSystem:
    """文本转SQL智能体系统主类"""
    
    def __init__(self):
        self.coordinator = coordinator
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            logger.info("正在初始化AI智能体文本转SQL系统...")
            
            # 测试数据库连接
            logger.info("测试数据库连接...")
            db_connected = await db_manager.test_connection()
            
            if not db_connected:
                logger.error("数据库连接失败，请检查配置")
                return False
            
            logger.info("数据库连接成功")
            
            # 获取工作流程状态
            workflow_status = await self.coordinator.get_workflow_status()
            logger.info(f"工作流程状态: {workflow_status.get('coordinator_status', 'unknown')}")
            
            self.is_initialized = True
            logger.info("系统初始化完成")
            
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    async def process_query(self, query: str, session_id: str = "default") -> Dict[str, Any]:
        """处理自然语言查询"""
        if not self.is_initialized:
            raise RuntimeError("系统未初始化，请先调用initialize()")
        
        try:
            logger.info(f"处理查询: {query[:100]}...")
            
            # 创建查询消息
            message = AgentMessage(
                type=MessageType.QUERY,
                content=query,
                metadata={"session_id": session_id},
                sender="user"
            )
            
            # 通过协调者处理查询
            result = await self.coordinator.process_message(message)
            
            if result.type == MessageType.ERROR:
                logger.error(f"查询处理失败: {result.content}")
                return {
                    "success": False,
                    "error": result.content,
                    "query": query
                }
            
            # 返回处理结果
            workflow_result = result.content
            
            # 生成摘要
            summary = self.coordinator.get_workflow_summary(workflow_result)
            workflow_result["summary_text"] = summary
            
            logger.info("查询处理完成")
            return workflow_result
            
        except Exception as e:
            logger.error(f"查询处理异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }
    
    async def process_batch_queries(self, queries: list, session_id: str = "batch") -> list:
        """批量处理查询"""
        if not self.is_initialized:
            raise RuntimeError("系统未初始化，请先调用initialize()")
        
        logger.info(f"开始批量处理 {len(queries)} 个查询")
        
        results = await self.coordinator.process_batch_queries(queries, session_id)
        
        logger.info(f"批量处理完成")
        return results
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 数据库状态
            db_status = await db_manager.test_connection()
            
            # 工作流程状态
            workflow_status = await self.coordinator.get_workflow_status()
            
            return {
                "system_initialized": self.is_initialized,
                "database_connected": db_status,
                "workflow_status": workflow_status,
                "settings": {
                    "database": settings.database.database,
                    "openai_model": settings.openai.model,
                    "security_enabled": settings.security.enable_sql_injection_check
                }
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                "system_initialized": self.is_initialized,
                "error": str(e)
            }


async def interactive_mode():
    """交互模式"""
    system = TextToSQLSystem()
    
    # 初始化系统
    if not await system.initialize():
        print("❌ 系统初始化失败，请检查配置")
        return
    
    print("🤖 AI智能体文本转SQL系统已启动")
    print("💡 输入自然语言查询，系统将自动转换为SQL并执行")
    print("💡 输入 'status' 查看系统状态")
    print("💡 输入 'quit' 或 'exit' 退出系统")
    print("-" * 60)
    
    session_id = "interactive"
    
    while True:
        try:
            # 获取用户输入
            query = input("\n🔍 请输入查询: ").strip()
            
            if not query:
                continue
            
            if query.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if query.lower() == 'status':
                status = await system.get_system_status()
                print(f"\n📊 系统状态:")
                print(f"   • 系统初始化: {'✅' if status['system_initialized'] else '❌'}")
                print(f"   • 数据库连接: {'✅' if status['database_connected'] else '❌'}")
                print(f"   • 数据库名称: {status.get('settings', {}).get('database', 'N/A')}")
                print(f"   • AI模型: {status.get('settings', {}).get('openai_model', 'N/A')}")
                continue
            
            # 处理查询
            print("\n⏳ 正在处理查询...")
            result = await system.process_query(query, session_id)
            
            if result.get("success", False):
                # 显示格式化结果
                final_result = result.get("final_result", {})
                formatted_output = final_result.get("formatted_output", "处理完成")
                print(f"\n{formatted_output}")
                
                # 显示摘要
                summary = result.get("summary_text", "")
                if summary:
                    print(f"\n{summary}")
            else:
                # 显示错误信息
                error = result.get("error", {})
                if isinstance(error, dict):
                    error_msg = error.get("error", "未知错误")
                else:
                    error_msg = str(error)
                print(f"\n❌ 处理失败: {error_msg}")
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            logger.error(f"交互模式异常: {e}")
            print(f"\n❌ 系统错误: {e}")


async def demo_mode():
    """演示模式"""
    system = TextToSQLSystem()
    
    # 初始化系统
    if not await system.initialize():
        print("❌ 系统初始化失败，请检查配置")
        return
    
    print("🎯 AI智能体文本转SQL系统演示")
    print("-" * 40)
    
    # 演示查询
    demo_queries = [
        "查询所有用户信息",
        "统计每个部门的员工数量",
        "查找工资大于5000的员工",
        "按年龄排序显示前10名员工"
    ]
    
    for i, query in enumerate(demo_queries, 1):
        print(f"\n📝 演示查询 {i}: {query}")
        print("⏳ 处理中...")
        
        result = await system.process_query(query, f"demo_{i}")
        
        if result.get("success", False):
            summary = result.get("summary_text", "处理完成")
            print(f"✅ {summary}")
        else:
            error = result.get("error", "未知错误")
            print(f"❌ 失败: {error}")
        
        print("-" * 40)


async def main():
    """主函数"""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode == "demo":
            await demo_mode()
        elif mode == "interactive":
            await interactive_mode()
        else:
            print("用法: python3 main.py [interactive|demo]")
    else:
        await interactive_mode()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        print(f"程序异常退出: {e}")
