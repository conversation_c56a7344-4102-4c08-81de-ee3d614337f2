"""
集成测试
测试整个系统的端到端功能
"""

import pytest
import asyncio
from unittest.mock import patch, AsyncMock

from main import TextToSQLSystem
from agents.base_agent import AgentMessage, MessageType
from test.conftest import TEST_QUERIES


@pytest.mark.asyncio
class TestSystemIntegration:
    """系统集成测试"""
    
    @pytest.fixture
    async def text_to_sql_system(self):
        """文本转SQL系统夹具"""
        system = TextToSQLSystem()
        # 注意：在实际测试中可能需要模拟数据库连接
        return system
    
    async def test_system_initialization(self, text_to_sql_system):
        """测试系统初始化"""
        # 模拟数据库连接成功
        with patch('database.connection.db_manager.test_connection', return_value=True):
            success = await text_to_sql_system.initialize()
            assert success
            assert text_to_sql_system.is_initialized
    
    async def test_system_initialization_failure(self, text_to_sql_system):
        """测试系统初始化失败"""
        # 模拟数据库连接失败
        with patch('database.connection.db_manager.test_connection', return_value=False):
            success = await text_to_sql_system.initialize()
            assert not success
            assert not text_to_sql_system.is_initialized
    
    async def test_process_query_without_initialization(self, text_to_sql_system):
        """测试未初始化时处理查询"""
        with pytest.raises(RuntimeError, match="系统未初始化"):
            await text_to_sql_system.process_query("查询用户信息")
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    @patch('agents.coordinator_agent.coordinator.process_message')
    async def test_process_query_success(self, mock_process_message, mock_db_test, text_to_sql_system):
        """测试成功处理查询"""
        # 初始化系统
        await text_to_sql_system.initialize()
        
        # 模拟协调者返回成功结果
        mock_result = AgentMessage(
            type=MessageType.FORMAT,
            content={
                "success": True,
                "final_result": {
                    "formatted_output": "查询成功",
                    "summary": {"message": "返回2行数据"}
                },
                "total_duration_ms": 150
            },
            sender="coordinator"
        )
        mock_process_message.return_value = mock_result
        
        # 处理查询
        result = await text_to_sql_system.process_query("查询用户信息")
        
        assert result["success"] is True
        assert "final_result" in result
        assert "summary_text" in result
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    @patch('agents.coordinator_agent.coordinator.process_message')
    async def test_process_query_failure(self, mock_process_message, mock_db_test, text_to_sql_system):
        """测试查询处理失败"""
        # 初始化系统
        await text_to_sql_system.initialize()
        
        # 模拟协调者返回错误结果
        mock_result = AgentMessage(
            type=MessageType.ERROR,
            content={"error": "SQL语法错误"},
            sender="coordinator"
        )
        mock_process_message.return_value = mock_result
        
        # 处理查询
        result = await text_to_sql_system.process_query("无效查询")
        
        assert result["success"] is False
        assert "error" in result
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    @patch('agents.coordinator_agent.coordinator.process_batch_queries')
    async def test_process_batch_queries(self, mock_batch_process, mock_db_test, text_to_sql_system):
        """测试批量查询处理"""
        # 初始化系统
        await text_to_sql_system.initialize()
        
        # 模拟批量处理结果
        mock_batch_process.return_value = [
            {"success": True, "query": "查询1"},
            {"success": True, "query": "查询2"}
        ]
        
        # 处理批量查询
        queries = ["查询用户信息", "查询订单信息"]
        results = await text_to_sql_system.process_batch_queries(queries)
        
        assert len(results) == 2
        assert all(result["success"] for result in results)
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    async def test_get_system_status(self, mock_db_test, text_to_sql_system):
        """测试获取系统状态"""
        # 初始化系统
        await text_to_sql_system.initialize()
        
        # 模拟工作流程状态
        with patch('agents.coordinator_agent.coordinator.get_workflow_status') as mock_workflow_status:
            mock_workflow_status.return_value = {
                "coordinator_status": "active",
                "total_workflows": 0
            }
            
            status = await text_to_sql_system.get_system_status()
            
            assert status["system_initialized"] is True
            assert status["database_connected"] is True
            assert "workflow_status" in status
            assert "settings" in status


@pytest.mark.asyncio
class TestWorkflowIntegration:
    """工作流程集成测试"""
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    @patch('database.connection.db_manager.get_all_tables', return_value=["users", "orders"])
    @patch('database.connection.db_manager.get_table_schema')
    async def test_complete_workflow_mock(self, mock_schema, mock_tables, mock_db_test):
        """测试完整工作流程（模拟）"""
        # 模拟表结构
        mock_schema.return_value = [
            {
                "COLUMN_NAME": "id",
                "DATA_TYPE": "int",
                "IS_NULLABLE": "NO",
                "COLUMN_DEFAULT": None,
                "COLUMN_COMMENT": "主键"
            },
            {
                "COLUMN_NAME": "name",
                "DATA_TYPE": "varchar",
                "IS_NULLABLE": "YES",
                "COLUMN_DEFAULT": None,
                "COLUMN_COMMENT": "姓名"
            }
        ]
        
        # 模拟OpenAI响应
        with patch('agents.sql_expert_agent.SQLExpertAgent._create_model_client') as mock_client:
            mock_assistant = AsyncMock()
            mock_response = AsyncMock()
            mock_response.chat_message.content = '{"sql": "SELECT * FROM users LIMIT 10", "explanation": "查询用户", "confidence": 0.9}'
            mock_assistant.on_messages.return_value = mock_response
            
            # 模拟数据库执行
            with patch('database.connection.db_manager.execute_query_to_dataframe') as mock_execute:
                import pandas as pd
                mock_execute.return_value = pd.DataFrame([
                    {"id": 1, "name": "张三"},
                    {"id": 2, "name": "李四"}
                ])
                
                # 创建系统并初始化
                system = TextToSQLSystem()
                await system.initialize()
                
                # 处理查询
                result = await system.process_query("查询所有用户")
                
                # 验证结果
                assert result is not None
                # 注意：由于模拟的复杂性，这里主要验证流程能够执行


@pytest.mark.asyncio
class TestErrorHandling:
    """错误处理集成测试"""
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    async def test_sql_expert_error_handling(self, mock_db_test):
        """测试SQL专家错误处理"""
        from agents.sql_expert_agent import sql_expert
        
        # 测试无效消息类型
        invalid_message = AgentMessage(
            type=MessageType.RESULT,
            content="invalid",
            sender="test"
        )
        
        result = await sql_expert.process_message(invalid_message)
        assert result.type == MessageType.ERROR
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    async def test_security_agent_error_handling(self, mock_db_test):
        """测试安全审查错误处理"""
        from agents.security_agent import security_agent
        
        # 测试危险SQL
        dangerous_message = AgentMessage(
            type=MessageType.SQL,
            content={"sql": "DROP TABLE users"},
            sender="sql_expert"
        )
        
        result = await security_agent.process_message(dangerous_message)
        assert result.type == MessageType.ERROR
        assert result.content["status"] == "rejected"
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    async def test_executor_agent_error_handling(self, mock_db_test):
        """测试执行器错误处理"""
        from agents.executor_agent import executor_agent
        
        # 测试未验证的消息
        unvalidated_message = AgentMessage(
            type=MessageType.SQL,
            content="SELECT * FROM users",
            sender="test"
        )
        
        result = await executor_agent.process_message(unvalidated_message)
        assert result.type == MessageType.ERROR


@pytest.mark.asyncio
class TestPerformance:
    """性能测试"""
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    async def test_concurrent_queries(self, mock_db_test):
        """测试并发查询处理"""
        system = TextToSQLSystem()
        await system.initialize()
        
        # 模拟协调者处理
        with patch('agents.coordinator_agent.coordinator.process_message') as mock_process:
            mock_result = AgentMessage(
                type=MessageType.FORMAT,
                content={"success": True, "final_result": {"formatted_output": "成功"}},
                sender="coordinator"
            )
            mock_process.return_value = mock_result
            
            # 并发处理多个查询
            queries = TEST_QUERIES[:3]  # 取前3个查询
            tasks = [system.process_query(query, f"session_{i}") for i, query in enumerate(queries)]
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 3
            assert all(result["success"] for result in results)
    
    @patch('database.connection.db_manager.test_connection', return_value=True)
    async def test_query_timeout_handling(self, mock_db_test):
        """测试查询超时处理"""
        system = TextToSQLSystem()
        await system.initialize()
        
        # 模拟超时
        with patch('agents.coordinator_agent.coordinator.process_message') as mock_process:
            async def slow_process(*args, **kwargs):
                await asyncio.sleep(0.1)  # 模拟慢查询
                return AgentMessage(
                    type=MessageType.FORMAT,
                    content={"success": True},
                    sender="coordinator"
                )
            
            mock_process.side_effect = slow_process
            
            # 使用超时处理查询
            try:
                result = await asyncio.wait_for(
                    system.process_query("慢查询"),
                    timeout=0.05  # 50ms超时
                )
                assert False, "应该超时"
            except asyncio.TimeoutError:
                assert True, "正确处理超时"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
