"""
pytest配置文件
定义测试夹具和配置
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from database.connection import DatabaseManager
from agents.base_agent import AgentMessage, MessageType


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_db_manager():
    """测试数据库管理器夹具"""
    # 使用测试数据库配置
    test_db_manager = DatabaseManager()
    
    # 测试连接
    try:
        is_connected = await test_db_manager.test_connection()
        if not is_connected:
            pytest.skip("数据库连接失败，跳过数据库相关测试")
    except Exception as e:
        pytest.skip(f"数据库连接异常: {e}")
    
    yield test_db_manager


@pytest.fixture
def sample_query_message():
    """示例查询消息夹具"""
    return AgentMessage(
        type=MessageType.QUERY,
        content="查询所有用户信息",
        metadata={"session_id": "test_session"},
        sender="test_user"
    )


@pytest.fixture
def sample_sql_message():
    """示例SQL消息夹具"""
    return AgentMessage(
        type=MessageType.SQL,
        content={
            "sql": "SELECT * FROM users LIMIT 10",
            "explanation": "查询用户表的前10条记录",
            "parameters": [],
            "confidence": 0.9
        },
        metadata={"original_query": "查询所有用户信息"},
        sender="sql_expert"
    )


@pytest.fixture
def sample_validation_message():
    """示例验证消息夹具"""
    return AgentMessage(
        type=MessageType.VALIDATION,
        content={
            "status": "approved",
            "sql": "SELECT * FROM users LIMIT 10",
            "security_checks": {
                "is_safe": True,
                "threats": [],
                "warnings": []
            }
        },
        sender="security_agent"
    )


@pytest.fixture
def sample_result_message():
    """示例结果消息夹具"""
    return AgentMessage(
        type=MessageType.RESULT,
        content={
            "success": True,
            "data": [
                {"id": 1, "name": "张三", "age": 25},
                {"id": 2, "name": "李四", "age": 30}
            ],
            "columns": ["id", "name", "age"],
            "row_count": 2,
            "execution_time_ms": 15.5,
            "sql_type": "SELECT"
        },
        sender="executor_agent"
    )


@pytest.fixture
def mock_openai_response():
    """模拟OpenAI响应夹具"""
    return {
        "sql": "SELECT * FROM users WHERE age > 25",
        "explanation": "查询年龄大于25的用户",
        "parameters": [],
        "estimated_rows": "未知",
        "confidence": 0.85
    }


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    
    # 创建测试日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    yield
    
    # 清理测试环境
    if "TESTING" in os.environ:
        del os.environ["TESTING"]


class MockOpenAIClient:
    """模拟OpenAI客户端"""
    
    def __init__(self, response_data=None):
        self.response_data = response_data or {
            "sql": "SELECT * FROM test_table",
            "explanation": "测试查询",
            "parameters": [],
            "confidence": 0.8
        }
    
    async def on_messages(self, messages, cancellation_token=None):
        """模拟消息处理"""
        class MockResponse:
            def __init__(self, content):
                self.chat_message = MockMessage(content)
        
        class MockMessage:
            def __init__(self, content):
                self.content = content
        
        import json
        return MockResponse(json.dumps(self.response_data))


@pytest.fixture
def mock_openai_client():
    """模拟OpenAI客户端夹具"""
    return MockOpenAIClient()


# 测试数据
TEST_QUERIES = [
    "查询所有用户信息",
    "统计每个部门的员工数量",
    "查找工资大于5000的员工",
    "按年龄排序显示前10名员工",
    "查询最近一周的订单",
    "计算平均工资"
]

DANGEROUS_SQL_QUERIES = [
    "DROP TABLE users",
    "DELETE FROM users",
    "SELECT * FROM users; DROP TABLE users;",
    "SELECT * FROM users WHERE id = 1 OR 1=1",
    "INSERT INTO users VALUES (1, 'admin'); --",
    "UNION SELECT password FROM admin_users"
]

VALID_SQL_QUERIES = [
    "SELECT * FROM users LIMIT 10",
    "SELECT name, age FROM users WHERE age > 25",
    "SELECT COUNT(*) FROM orders WHERE created_at > '2023-01-01'",
    "SELECT department, COUNT(*) as count FROM employees GROUP BY department",
    "UPDATE users SET last_login = NOW() WHERE id = 1",
    "INSERT INTO logs (message, created_at) VALUES ('test', NOW())"
]
