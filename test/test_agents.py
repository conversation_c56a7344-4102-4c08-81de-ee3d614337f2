"""
智能体模块测试
测试各个智能体的功能
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch

from agents.base_agent import BaseAgent, AgentMessage, MessageType
from agents.sql_expert_agent import SQLExpertAgent
from agents.security_agent import SecurityAgent
from agents.executor_agent import ExecutorAgent
from agents.formatter_agent import FormatterAgent
from agents.coordinator_agent import CoordinatorAgent


class TestBaseAgent:
    """基础智能体测试"""
    
    def test_agent_message_creation(self):
        """测试消息创建"""
        message = AgentMessage(
            type=MessageType.QUERY,
            content="test query",
            metadata={"session_id": "test"},
            sender="user"
        )
        
        assert message.type == MessageType.QUERY
        assert message.content == "test query"
        assert message.metadata["session_id"] == "test"
        assert message.sender == "user"
    
    def test_agent_message_to_dict(self):
        """测试消息转字典"""
        message = AgentMessage(
            type=MessageType.QUERY,
            content="test query",
            metadata={"session_id": "test"},
            sender="user"
        )
        
        data = message.to_dict()
        assert data["type"] == "query"
        assert data["content"] == "test query"
        assert data["metadata"]["session_id"] == "test"
        assert data["sender"] == "user"
    
    def test_agent_message_from_dict(self):
        """测试从字典创建消息"""
        data = {
            "type": "query",
            "content": "test query",
            "metadata": {"session_id": "test"},
            "sender": "user"
        }
        
        message = AgentMessage.from_dict(data)
        assert message.type == MessageType.QUERY
        assert message.content == "test query"
        assert message.metadata["session_id"] == "test"
        assert message.sender == "user"


class TestSQLExpertAgent:
    """SQL专家智能体测试"""
    
    @pytest.fixture
    def sql_expert(self):
        """SQL专家智能体夹具"""
        return SQLExpertAgent()
    
    def test_sql_expert_init(self, sql_expert):
        """测试SQL专家初始化"""
        assert sql_expert.name == "SQL专家"
        assert "自然语言" in sql_expert.description
        assert sql_expert.schema_cache == {}
    
    @pytest.mark.asyncio
    async def test_process_non_query_message(self, sql_expert):
        """测试处理非查询消息"""
        message = AgentMessage(
            type=MessageType.SQL,
            content="SELECT * FROM users",
            sender="test"
        )
        
        result = await sql_expert.process_message(message)
        assert result.type == MessageType.ERROR
        assert "只处理查询类型" in result.content["error"]
    
    @pytest.mark.asyncio
    @patch('agents.sql_expert_agent.db_manager')
    async def test_get_database_schema(self, mock_db_manager, sql_expert):
        """测试获取数据库模式"""
        # 模拟数据库返回
        mock_db_manager.get_all_tables.return_value = ["users", "orders"]
        mock_db_manager.get_table_schema.return_value = [
            {
                "COLUMN_NAME": "id",
                "DATA_TYPE": "int",
                "IS_NULLABLE": "NO",
                "COLUMN_DEFAULT": None,
                "COLUMN_COMMENT": "主键"
            }
        ]
        
        schema = await sql_expert._get_database_schema()
        
        assert "database" in schema
        assert "tables" in schema
        assert "users" in schema["tables"]
        assert "orders" in schema["tables"]
    
    def test_format_schema_info(self, sql_expert):
        """测试格式化模式信息"""
        schema_info = {
            "database": "test_db",
            "tables": {
                "users": [
                    {
                        "COLUMN_NAME": "id",
                        "DATA_TYPE": "int",
                        "IS_NULLABLE": "NO",
                        "COLUMN_DEFAULT": None,
                        "COLUMN_COMMENT": "主键"
                    }
                ]
            }
        }
        
        formatted = sql_expert._format_schema_info(schema_info)
        assert "test_db" in formatted
        assert "users" in formatted
        assert "id: int NOT NULL" in formatted
    
    def test_parse_sql_response_json(self, sql_expert):
        """测试解析JSON响应"""
        json_response = json.dumps({
            "sql": "SELECT * FROM users",
            "explanation": "查询所有用户",
            "confidence": 0.9
        })
        
        result = sql_expert._parse_sql_response(json_response)
        assert result["sql"] == "SELECT * FROM users"
        assert result["explanation"] == "查询所有用户"
        assert result["confidence"] == 0.9
    
    def test_parse_sql_response_plain_text(self, sql_expert):
        """测试解析纯文本响应"""
        text_response = "SELECT * FROM users WHERE age > 25"
        
        result = sql_expert._parse_sql_response(text_response)
        assert result["sql"] == "SELECT * FROM users WHERE age > 25"
        assert "explanation" in result
        assert result["confidence"] == 0.7


class TestSecurityAgent:
    """安全审查智能体测试"""
    
    @pytest.fixture
    def security_agent(self):
        """安全审查智能体夹具"""
        return SecurityAgent()
    
    def test_security_agent_init(self, security_agent):
        """测试安全审查智能体初始化"""
        assert security_agent.name == "安全审查员"
        assert "安全性" in security_agent.description
    
    @pytest.mark.asyncio
    async def test_process_non_sql_message(self, security_agent):
        """测试处理非SQL消息"""
        message = AgentMessage(
            type=MessageType.QUERY,
            content="test query",
            sender="test"
        )
        
        result = await security_agent.process_message(message)
        assert result.type == MessageType.ERROR
        assert "只处理SQL类型" in result.content["error"]
    
    @pytest.mark.asyncio
    async def test_process_safe_sql(self, security_agent):
        """测试处理安全SQL"""
        message = AgentMessage(
            type=MessageType.SQL,
            content={
                "sql": "SELECT * FROM users LIMIT 10",
                "explanation": "查询用户"
            },
            sender="sql_expert"
        )
        
        result = await security_agent.process_message(message)
        assert result.type == MessageType.VALIDATION
        assert result.content["status"] == "approved"
    
    @pytest.mark.asyncio
    async def test_process_dangerous_sql(self, security_agent):
        """测试处理危险SQL"""
        message = AgentMessage(
            type=MessageType.SQL,
            content={
                "sql": "DROP TABLE users",
                "explanation": "删除用户表"
            },
            sender="sql_expert"
        )
        
        result = await security_agent.process_message(message)
        assert result.type == MessageType.ERROR
        assert result.content["status"] == "rejected"
        assert len(result.content["security_issues"]) > 0


class TestExecutorAgent:
    """执行器智能体测试"""
    
    @pytest.fixture
    def executor_agent(self):
        """执行器智能体夹具"""
        return ExecutorAgent()
    
    def test_executor_agent_init(self, executor_agent):
        """测试执行器智能体初始化"""
        assert executor_agent.name == "数据库执行器"
        assert "执行" in executor_agent.description
        assert executor_agent.max_result_rows == 1000
    
    @pytest.mark.asyncio
    async def test_process_non_validation_message(self, executor_agent):
        """测试处理非验证消息"""
        message = AgentMessage(
            type=MessageType.SQL,
            content="SELECT * FROM users",
            sender="test"
        )
        
        result = await executor_agent.process_message(message)
        assert result.type == MessageType.ERROR
        assert "只处理经过验证" in result.content["error"]
    
    @pytest.mark.asyncio
    async def test_process_rejected_validation(self, executor_agent):
        """测试处理被拒绝的验证"""
        message = AgentMessage(
            type=MessageType.VALIDATION,
            content={
                "status": "rejected",
                "sql": "DROP TABLE users"
            },
            sender="security_agent"
        )
        
        result = await executor_agent.process_message(message)
        assert result.type == MessageType.ERROR
        assert "未通过安全验证" in result.content["error"]
    
    def test_get_sql_type(self, executor_agent):
        """测试SQL类型识别"""
        test_cases = [
            ("SELECT * FROM users", "SELECT"),
            ("INSERT INTO users VALUES (1, 'test')", "INSERT"),
            ("UPDATE users SET name = 'test'", "UPDATE"),
            ("DELETE FROM users WHERE id = 1", "DELETE"),
            ("UNKNOWN STATEMENT", "UNKNOWN")
        ]
        
        for sql, expected_type in test_cases:
            result = executor_agent._get_sql_type(sql)
            assert result == expected_type


class TestFormatterAgent:
    """格式化智能体测试"""
    
    @pytest.fixture
    def formatter_agent(self):
        """格式化智能体夹具"""
        return FormatterAgent()
    
    def test_formatter_agent_init(self, formatter_agent):
        """测试格式化智能体初始化"""
        assert formatter_agent.name == "结果格式化器"
        assert "格式化" in formatter_agent.description
    
    @pytest.mark.asyncio
    async def test_process_non_result_message(self, formatter_agent):
        """测试处理非结果消息"""
        message = AgentMessage(
            type=MessageType.SQL,
            content="SELECT * FROM users",
            sender="test"
        )
        
        result = await formatter_agent.process_message(message)
        assert result.type == MessageType.ERROR
        assert "只处理结果类型" in result.content["error"]
    
    @pytest.mark.asyncio
    async def test_format_select_result(self, formatter_agent, sample_result_message):
        """测试格式化SELECT结果"""
        result = await formatter_agent.process_message(sample_result_message)
        
        assert result.type == MessageType.FORMAT
        assert "formatted_output" in result.content
        assert "summary" in result.content
        assert "formats" in result.content
    
    def test_create_table_format(self, formatter_agent):
        """测试创建表格格式"""
        data = [
            {"id": 1, "name": "张三", "age": 25},
            {"id": 2, "name": "李四", "age": 30}
        ]
        columns = ["id", "name", "age"]
        
        table = formatter_agent._create_table_format(data, columns)
        assert "张三" in table
        assert "李四" in table
        assert "id" in table
    
    def test_create_csv_format(self, formatter_agent):
        """测试创建CSV格式"""
        data = [
            {"id": 1, "name": "张三", "age": 25},
            {"id": 2, "name": "李四", "age": 30}
        ]
        columns = ["id", "name", "age"]
        
        csv = formatter_agent._create_csv_format(data, columns)
        lines = csv.split('\n')
        assert lines[0] == "id,name,age"
        assert "张三" in csv
        assert "李四" in csv


class TestCoordinatorAgent:
    """协调者智能体测试"""
    
    @pytest.fixture
    def coordinator_agent(self):
        """协调者智能体夹具"""
        return CoordinatorAgent()
    
    def test_coordinator_agent_init(self, coordinator_agent):
        """测试协调者智能体初始化"""
        assert coordinator_agent.name == "流程协调者"
        assert "协调" in coordinator_agent.description
        assert len(coordinator_agent.workflow_steps) == 4
    
    @pytest.mark.asyncio
    async def test_process_non_query_message(self, coordinator_agent):
        """测试处理非查询消息"""
        message = AgentMessage(
            type=MessageType.SQL,
            content="SELECT * FROM users",
            sender="test"
        )
        
        result = await coordinator_agent.process_message(message)
        assert result.type == MessageType.ERROR
        assert "只处理查询类型" in result.content["error"]
    
    @pytest.mark.asyncio
    async def test_get_workflow_status(self, coordinator_agent):
        """测试获取工作流程状态"""
        status = await coordinator_agent.get_workflow_status()
        
        assert "coordinator_status" in status
        assert "agent_statuses" in status
        assert "workflow_steps" in status
        assert len(status["workflow_steps"]) == 4


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
