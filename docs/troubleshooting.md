# 故障排除指南

## 常见问题和解决方案

### 1. 系统初始化问题

#### 问题：系统初始化失败
**症状**：
```
❌ 系统初始化失败，请检查配置
```

**可能原因和解决方案**：

1. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   sudo systemctl status mysql
   
   # 测试数据库连接
   mysql -h localhost -u your_user -p your_database
   ```

2. **配置文件问题**
   ```bash
   # 检查.env文件是否存在
   ls -la .env
   
   # 验证配置格式
   python3 -c "from config.settings import settings; print('配置正常')"
   ```

3. **权限问题**
   ```sql
   -- 检查用户权限
   SHOW GRANTS FOR 'your_user'@'localhost';
   
   -- 授予必要权限
   GRANT SELECT, INSERT, UPDATE, DELETE ON your_database.* TO 'your_user'@'localhost';
   ```

#### 问题：OpenAI API连接失败
**症状**：
```
Error: OpenAI API authentication failed
```

**解决方案**：
1. 验证API密钥
   ```bash
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

2. 检查网络连接
   ```bash
   ping api.openai.com
   ```

3. 检查API配额
   - 登录OpenAI控制台查看使用情况
   - 确认账户余额充足

### 2. 查询处理问题

#### 问题：SQL生成失败
**症状**：
```
❌ 处理失败: 无法生成有效的SQL语句
```

**解决方案**：

1. **检查查询描述**
   - 确保查询描述清晰明确
   - 避免过于复杂或模糊的表达
   - 提供足够的上下文信息

2. **检查数据库模式**
   ```python
   # 验证表结构获取
   from database.connection import db_manager
   tables = await db_manager.get_all_tables()
   print(f"可用表: {tables}")
   ```

3. **调整AI模型参数**
   ```bash
   # 尝试使用不同的模型
   OPENAI_MODEL=gpt-3.5-turbo
   ```

#### 问题：安全检查失败
**症状**：
```
❌ 失败: SQL未通过安全验证，拒绝执行
```

**解决方案**：

1. **检查SQL内容**
   - 避免使用危险关键词（DROP, DELETE等）
   - 确保查询符合安全策略

2. **调整安全配置**
   ```bash
   # 临时禁用严格检查（仅用于调试）
   ENABLE_SQL_INJECTION_CHECK=false
   ```

3. **查看详细错误信息**
   ```python
   # 启用调试日志
   LOG_LEVEL=DEBUG
   ```

#### 问题：SQL执行超时
**症状**：
```
❌ 失败: 查询执行超时
```

**解决方案**：

1. **增加超时时间**
   ```bash
   MAX_QUERY_TIMEOUT=60
   ```

2. **优化查询**
   - 添加适当的WHERE条件
   - 使用LIMIT限制结果数量
   - 检查表索引

3. **检查数据库性能**
   ```sql
   -- 查看正在运行的查询
   SHOW PROCESSLIST;
   
   -- 检查慢查询日志
   SHOW VARIABLES LIKE 'slow_query_log';
   ```

### 3. 性能问题

#### 问题：响应速度慢
**症状**：查询处理时间过长

**解决方案**：

1. **数据库优化**
   ```sql
   -- 添加索引
   CREATE INDEX idx_user_name ON users(name);
   
   -- 分析查询计划
   EXPLAIN SELECT * FROM users WHERE name = 'test';
   ```

2. **连接池优化**
   ```bash
   # 增加连接池大小
   DB_POOL_SIZE=20
   DB_MAX_OVERFLOW=50
   ```

3. **缓存优化**
   - 启用模式缓存
   - 使用查询结果缓存

#### 问题：内存使用过高
**症状**：系统内存占用持续增长

**解决方案**：

1. **限制结果集大小**
   ```python
   # 在executor_agent.py中调整
   self.max_result_rows = 500
   ```

2. **启用垃圾回收**
   ```python
   import gc
   gc.collect()
   ```

3. **监控内存使用**
   ```bash
   # 使用htop或top监控
   htop
   
   # Python内存分析
   pip install memory-profiler
   python3 -m memory_profiler main.py
   ```

### 4. 网络问题

#### 问题：网络连接不稳定
**症状**：间歇性连接失败

**解决方案**：

1. **增加重试机制**
   ```python
   # 在配置中添加重试设置
   MAX_RETRIES=3
   RETRY_DELAY=1
   ```

2. **使用连接池**
   - 确保连接池配置合理
   - 启用连接健康检查

3. **网络诊断**
   ```bash
   # 检查网络延迟
   ping -c 10 api.openai.com
   
   # 检查DNS解析
   nslookup api.openai.com
   ```

### 5. 日志和调试

#### 启用详细日志
```bash
# 设置调试级别
LOG_LEVEL=DEBUG

# 查看实时日志
tail -f logs/app.log
```

#### 调试特定组件
```python
# 调试SQL生成
from agents.sql_expert_agent import sql_expert
import logging
logging.getLogger('agents.sql_expert_agent').setLevel(logging.DEBUG)

# 调试数据库连接
from database.connection import db_manager
logging.getLogger('database.connection').setLevel(logging.DEBUG)
```

#### 性能分析
```python
# 添加性能监控
import time
import cProfile

def profile_query(query):
    start_time = time.time()
    cProfile.run('process_query(query)')
    end_time = time.time()
    print(f"查询耗时: {end_time - start_time:.2f}秒")
```

### 6. 环境问题

#### 问题：依赖包冲突
**症状**：
```
ImportError: cannot import name 'xxx' from 'yyy'
```

**解决方案**：

1. **创建虚拟环境**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **更新依赖包**
   ```bash
   pip install --upgrade -r requirements.txt
   ```

3. **检查Python版本**
   ```bash
   python3 --version
   # 确保使用Python 3.8+
   ```

#### 问题：权限错误
**症状**：
```
PermissionError: [Errno 13] Permission denied
```

**解决方案**：

1. **检查文件权限**
   ```bash
   chmod +x main.py
   chmod -R 755 logs/
   ```

2. **检查目录权限**
   ```bash
   mkdir -p logs
   chown -R $USER:$USER logs/
   ```

### 7. 数据库特定问题

#### MySQL连接问题
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 重启MySQL服务
sudo systemctl restart mysql

# 检查MySQL配置
sudo mysql -e "SHOW VARIABLES LIKE 'max_connections';"
```

#### 字符编码问题
```sql
-- 设置正确的字符集
ALTER DATABASE your_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 检查表字符集
SHOW CREATE TABLE your_table;
```

### 8. 监控和告警

#### 设置监控
```python
# 添加健康检查端点
from flask import Flask
app = Flask(__name__)

@app.route('/health')
async def health_check():
    status = await system.get_system_status()
    return status

# 运行监控服务
app.run(host='0.0.0.0', port=8080)
```

#### 日志监控
```bash
# 监控错误日志
tail -f logs/error.log | grep -i error

# 统计错误频率
grep -c "ERROR" logs/app.log
```

### 9. 紧急恢复

#### 系统重置
```bash
# 停止所有进程
pkill -f "python3 main.py"

# 清理临时文件
rm -rf __pycache__/
rm -rf .pytest_cache/

# 重新初始化
python3 main.py interactive
```

#### 数据库恢复
```sql
-- 检查数据库状态
SHOW ENGINE INNODB STATUS;

-- 修复表
REPAIR TABLE your_table;

-- 重建索引
ALTER TABLE your_table ENGINE=InnoDB;
```

### 10. 获取帮助

#### 收集诊断信息
```bash
# 生成诊断报告
python3 -c "
import sys
import platform
from config.settings import settings

print('=== 系统信息 ===')
print(f'Python版本: {sys.version}')
print(f'操作系统: {platform.system()} {platform.release()}')
print(f'架构: {platform.machine()}')

print('\n=== 配置信息 ===')
print(f'数据库主机: {settings.database.host}')
print(f'数据库端口: {settings.database.port}')
print(f'AI模型: {settings.openai.model}')
print(f'日志级别: {settings.logging.level}')

print('\n=== 依赖包版本 ===')
import pkg_resources
for package in ['autogen-agentchat', 'mysql-connector-python', 'openai']:
    try:
        version = pkg_resources.get_distribution(package).version
        print(f'{package}: {version}')
    except:
        print(f'{package}: 未安装')
"
```

#### 联系支持
如果问题仍然无法解决，请提供以下信息：

1. 错误日志（logs/app.log和logs/error.log）
2. 系统配置信息
3. 重现步骤
4. 环境信息（操作系统、Python版本等）

---

**注意**：在生产环境中进行任何更改之前，请务必备份数据和配置文件。
