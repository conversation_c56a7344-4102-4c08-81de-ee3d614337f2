# 技术架构文档

## 系统概述

AI智能体文本转SQL系统是一个基于AutoGen 0.6框架的多智能体协作系统，旨在将自然语言查询转换为安全、准确的SQL语句并自动执行。

## 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层                                │
├─────────────────────────────────────────────────────────────┤
│  交互模式  │  API接口  │  批量处理  │  Web界面(可扩展)      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  协调者智能体                                │
├─────────────────────────────────────────────────────────────┤
│  • 工作流程管理    • 消息路由    • 错误处理    • 状态监控   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  专业智能体层                                │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│ SQL专家     │ 安全审查    │ 数据库执行  │ 结果格式化          │
│ 智能体      │ 智能体      │ 智能体      │ 智能体              │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    基础设施层                                │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│ 数据库连接  │ 安全验证    │ 错误处理    │ 日志记录            │
│ 池管理      │ 模块        │ 机制        │ 系统                │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
```

## 核心组件

### 1. 智能体系统 (Agents)

#### 1.1 基础智能体 (BaseAgent)
- **职责**: 定义智能体通用接口和行为
- **功能**:
  - 消息处理抽象接口
  - 历史记录管理
  - 错误处理机制
  - 系统提示词管理

#### 1.2 SQL专家智能体 (SQLExpertAgent)
- **职责**: 自然语言到SQL的转换
- **核心功能**:
  - 基于OpenAI GPT-4的语言理解
  - 数据库模式感知
  - SQL语法优化
  - 查询性能建议
- **技术实现**:
  - AutoGen AssistantAgent集成
  - 数据库模式缓存
  - 动态提示词构建

#### 1.3 安全审查智能体 (SecurityAgent)
- **职责**: SQL安全验证和风险评估
- **安全检查项**:
  - SQL注入模式检测
  - 操作类型验证
  - 查询复杂度评估
  - 敏感操作警告
- **检测算法**:
  - 正则表达式模式匹配
  - SQL语法树分析
  - 危险关键词识别

#### 1.4 数据库执行智能体 (ExecutorAgent)
- **职责**: 安全执行SQL语句
- **核心功能**:
  - 连接池管理
  - 查询超时控制
  - 结果集处理
  - 事务管理
- **性能优化**:
  - 异步执行
  - 结果分页
  - 内存管理

#### 1.5 结果格式化智能体 (FormatterAgent)
- **职责**: 结果美化和多格式输出
- **输出格式**:
  - 表格格式 (tabulate)
  - Rich表格 (rich)
  - JSON格式
  - CSV格式
  - 统计摘要
- **可视化特性**:
  - 颜色高亮
  - 数据类型识别
  - 统计信息计算

#### 1.6 协调者智能体 (CoordinatorAgent)
- **职责**: 工作流程编排和管理
- **工作流程**:
  1. 接收用户查询
  2. 调用SQL专家生成SQL
  3. 安全审查验证
  4. 数据库执行
  5. 结果格式化
  6. 返回最终结果
- **管理功能**:
  - 智能体间通信
  - 错误恢复机制
  - 性能监控
  - 批量处理

### 2. 数据库层 (Database)

#### 2.1 连接管理 (DatabaseManager)
- **连接池**: MySQL连接池管理
- **配置**: 动态配置和热重载
- **监控**: 连接状态和性能监控
- **安全**: SSL连接和权限控制

#### 2.2 安全验证 (SQLSecurityValidator)
- **注入检测**: 多层次SQL注入防护
- **操作限制**: 可配置的操作类型控制
- **复杂度控制**: 查询复杂度和资源限制
- **审计日志**: 完整的安全事件记录

### 3. 配置系统 (Configuration)

#### 3.1 设置管理 (Settings)
- **分层配置**: 数据库、AI、安全、日志配置
- **环境变量**: .env文件支持
- **验证机制**: Pydantic数据验证
- **热重载**: 运行时配置更新

### 4. 工具模块 (Utils)

#### 4.1 错误处理 (ErrorHandler)
- **异常分类**: 智能异常类型识别
- **错误恢复**: 自动重试和降级机制
- **用户友好**: 清晰的错误信息和建议
- **调试支持**: 详细的堆栈跟踪

#### 4.2 日志系统 (Logger)
- **多级日志**: DEBUG、INFO、WARNING、ERROR
- **多输出**: 控制台、文件、远程日志
- **结构化**: JSON格式日志支持
- **性能**: 异步日志写入

## 数据流

### 查询处理流程

```
用户查询 → 协调者智能体 → SQL专家智能体 → 安全审查智能体 → 数据库执行智能体 → 结果格式化智能体 → 用户
    ↓           ↓              ↓              ↓                ↓                  ↓
  验证输入   → 路由消息    → 生成SQL      → 安全检查        → 执行查询        → 格式化结果
```

### 消息传递机制

```python
class AgentMessage:
    type: MessageType      # 消息类型
    content: Any          # 消息内容
    metadata: Dict        # 元数据
    sender: str          # 发送者
    timestamp: str       # 时间戳
```

### 错误处理流程

```
异常发生 → 错误分类 → 错误包装 → 日志记录 → 用户反馈 → 恢复策略
```

## 安全架构

### 多层安全防护

1. **输入验证层**
   - 参数类型检查
   - 长度限制
   - 字符过滤

2. **SQL安全层**
   - 注入模式检测
   - 危险操作拦截
   - 语法验证

3. **执行安全层**
   - 权限检查
   - 资源限制
   - 超时控制

4. **输出安全层**
   - 敏感信息过滤
   - 数据脱敏
   - 访问日志

### 安全配置

```python
class SecuritySettings:
    max_query_timeout: int = 30
    allowed_operations: List[str] = ["SELECT", "INSERT", "UPDATE", "DELETE"]
    enable_sql_injection_check: bool = True
    max_result_rows: int = 1000
```

## 性能优化

### 1. 数据库优化
- **连接池**: 复用数据库连接
- **查询缓存**: 模式信息缓存
- **索引建议**: 自动索引优化建议

### 2. 智能体优化
- **模型缓存**: OpenAI响应缓存
- **并发处理**: 异步智能体执行
- **批量操作**: 批量查询优化

### 3. 内存优化
- **流式处理**: 大结果集流式处理
- **垃圾回收**: 主动内存管理
- **对象池**: 对象复用机制

## 扩展性设计

### 1. 智能体扩展
- **插件机制**: 动态智能体加载
- **自定义智能体**: 用户定义智能体
- **智能体链**: 复杂工作流程支持

### 2. 数据库扩展
- **多数据库**: PostgreSQL、SQLite支持
- **NoSQL**: MongoDB、Redis集成
- **分布式**: 分布式数据库支持

### 3. AI模型扩展
- **多模型**: Claude、Gemini支持
- **本地模型**: 开源模型集成
- **模型切换**: 动态模型选择

## 监控和运维

### 1. 性能监控
- **响应时间**: 端到端延迟监控
- **吞吐量**: QPS和并发监控
- **资源使用**: CPU、内存、网络监控

### 2. 业务监控
- **查询统计**: 查询类型和频率
- **成功率**: 查询成功率监控
- **错误分析**: 错误类型和趋势

### 3. 告警机制
- **阈值告警**: 性能指标告警
- **异常告警**: 错误率告警
- **安全告警**: 安全事件告警

## 部署架构

### 1. 单机部署
```
┌─────────────────┐
│   应用服务器     │
├─────────────────┤
│ • Python应用    │
│ • 智能体系统    │
│ • 配置管理      │
└─────────────────┘
        │
┌─────────────────┐
│   MySQL数据库   │
└─────────────────┘
```

### 2. 分布式部署
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│  负载均衡   │  │   应用集群   │  │   数据库集群 │
│   (Nginx)   │  │ (多实例)    │  │  (主从)     │
└─────────────┘  └─────────────┘  └─────────────┘
```

### 3. 容器化部署
```yaml
version: '3.8'
services:
  app:
    image: ai-text-to-sql:latest
    environment:
      - DB_HOST=mysql
      - OPENAI_API_KEY=${OPENAI_API_KEY}
  mysql:
    image: mysql:8.4
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
```

## 总结

本系统采用微服务架构思想，通过多智能体协作实现复杂的自然语言到SQL转换任务。每个智能体专注于特定领域，通过标准化的消息传递机制协作完成整体任务。系统具有良好的可扩展性、安全性和性能，适合在生产环境中部署使用。
