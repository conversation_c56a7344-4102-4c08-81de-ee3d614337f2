# AI智能体文本转SQL系统

基于AutoGen 0.6框架的多智能体系统，能够将自然语言查询转换为MySQL SQL语句并自动执行。

## 🚀 功能特性

- **自然语言理解**: 支持中文自然语言查询转换为SQL
- **多智能体协作**: 基于AutoGen 0.6的专业智能体分工协作
- **安全防护**: 内置SQL注入检测和安全验证机制
- **自动执行**: 安全执行生成的SQL查询并返回结果
- **结果格式化**: 多种格式的结果展示（表格、JSON、CSV等）
- **错误处理**: 完善的错误处理和日志记录机制

## 🏗️ 系统架构

### 智能体角色设计

1. **SQL专家智能体** (`SQLExpertAgent`)
   - 负责将自然语言转换为SQL语句
   - 基于数据库模式生成准确的查询
   - 支持复杂查询和优化建议

2. **安全审查智能体** (`SecurityAgent`)
   - SQL注入检测和防护
   - 操作类型验证
   - 查询复杂度检查

3. **数据库执行智能体** (`ExecutorAgent`)
   - 安全执行经过验证的SQL语句
   - 连接池管理和超时控制
   - 结果数据处理

4. **结果格式化智能体** (`FormatterAgent`)
   - 多格式结果展示
   - 数据可视化和美化
   - 统计信息生成

5. **协调者智能体** (`CoordinatorAgent`)
   - 管理整个工作流程
   - 智能体间消息传递
   - 错误处理和重试机制

### 技术栈

- **框架**: AutoGen 0.6
- **数据库**: MySQL 8.4
- **语言**: Python 3.8+
- **AI模型**: OpenAI GPT-4
- **安全**: sqlparse, 自定义安全验证
- **数据处理**: pandas, tabulate, rich

## 📦 安装部署

### 环境要求

- Python 3.8+
- MySQL 8.4+
- OpenAI API Key

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-agent-textToSQL1
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和OpenAI API
```

4. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE your_database_name;

-- 创建用户并授权
CREATE USER 'your_db_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON your_database_name.* TO 'your_db_user'@'localhost';
```

## 🎯 使用方法

### 交互模式

```bash
python3 main.py interactive
```

### 演示模式

```bash
python3 main.py demo
```

### 编程接口

```python
from main import TextToSQLSystem

# 创建系统实例
system = TextToSQLSystem()

# 初始化系统
await system.initialize()

# 处理查询
result = await system.process_query("查询所有用户信息")

# 批量处理
queries = ["查询用户数量", "统计订单总数"]
results = await system.process_batch_queries(queries)
```

## 📝 查询示例

### 基础查询
- "查询所有用户信息"
- "显示前10个订单"
- "查找名字叫张三的用户"

### 聚合查询
- "统计每个部门的员工数量"
- "计算平均工资"
- "查询最高销售额"

### 复杂查询
- "查询最近一周的订单，按金额排序"
- "统计每个月的销售趋势"
- "查找工资大于平均工资的员工"

## 🔒 安全特性

### SQL注入防护
- 危险关键词检测
- 注入模式识别
- 参数化查询支持

### 操作限制
- 可配置的允许操作类型
- 查询复杂度限制
- 执行超时控制

### 审计日志
- 完整的操作记录
- 安全事件追踪
- 性能监控

## 🧪 测试

### 运行所有测试
```bash
pytest test/ -v
```

### 运行特定测试
```bash
# 安全测试
pytest test/test_security.py -v

# 智能体测试
pytest test/test_agents.py -v

# 集成测试
pytest test/test_integration.py -v
```

### 测试覆盖率
```bash
pytest test/ --cov=. --cov-report=html
```

## 📊 性能优化

### 数据库优化
- 连接池管理
- 查询缓存
- 索引建议

### 智能体优化
- 模式缓存
- 批量处理
- 异步执行

## 🔧 配置说明

详细配置说明请参考 [配置文档](./configuration.md)

## 🐛 故障排除

常见问题和解决方案请参考 [故障排除指南](./troubleshooting.md)

## 📚 API文档

完整的API文档请参考 [API参考](./api_reference.md)

## 🤝 贡献指南

欢迎贡献代码！请参考 [贡献指南](./contributing.md)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](../LICENSE) 文件

## 📞 支持

如有问题或建议，请：
- 提交 Issue
- 发送邮件至 <EMAIL>
- 查看文档和FAQ

---

**注意**: 本系统仅用于学习和研究目的，生产环境使用请确保充分测试和安全评估。
