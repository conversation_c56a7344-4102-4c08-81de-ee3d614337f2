# API参考文档

## 概述

本文档描述了AI智能体文本转SQL系统的主要API接口和使用方法。

## 主要类和接口

### TextToSQLSystem

系统主类，提供完整的文本转SQL功能。

#### 初始化

```python
from main import TextToSQLSystem

system = TextToSQLSystem()
```

#### 方法

##### `async initialize() -> bool`

初始化系统，包括数据库连接测试和智能体准备。

**返回值**:
- `bool`: 初始化是否成功

**示例**:
```python
success = await system.initialize()
if success:
    print("系统初始化成功")
else:
    print("系统初始化失败")
```

##### `async process_query(query: str, session_id: str = "default") -> Dict[str, Any]`

处理单个自然语言查询。

**参数**:
- `query` (str): 自然语言查询
- `session_id` (str, 可选): 会话ID，默认为"default"

**返回值**:
- `Dict[str, Any]`: 查询结果字典

**返回结构**:
```python
{
    "success": bool,                    # 是否成功
    "query": str,                       # 原始查询
    "session_id": str,                  # 会话ID
    "start_time": str,                  # 开始时间
    "end_time": str,                    # 结束时间
    "total_duration_ms": float,         # 总耗时(毫秒)
    "steps": {                          # 各步骤详情
        "sql_generation": {...},
        "security_validation": {...},
        "sql_execution": {...},
        "result_formatting": {...}
    },
    "final_result": {                   # 最终结果
        "formatted_output": str,        # 格式化输出
        "summary": {...},               # 结果摘要
        "formats": {...}                # 多种格式
    },
    "summary_text": str                 # 摘要文本
}
```

**示例**:
```python
result = await system.process_query("查询所有用户信息")
if result["success"]:
    print(result["final_result"]["formatted_output"])
else:
    print(f"查询失败: {result['error']}")
```

##### `async process_batch_queries(queries: List[str], session_id: str = "batch") -> List[Dict[str, Any]]`

批量处理多个查询。

**参数**:
- `queries` (List[str]): 查询列表
- `session_id` (str, 可选): 会话ID前缀

**返回值**:
- `List[Dict[str, Any]]`: 结果列表

**示例**:
```python
queries = ["查询用户数量", "统计订单总数"]
results = await system.process_batch_queries(queries)
for i, result in enumerate(results):
    print(f"查询 {i+1}: {'成功' if result['success'] else '失败'}")
```

##### `async get_system_status() -> Dict[str, Any]`

获取系统状态信息。

**返回值**:
- `Dict[str, Any]`: 系统状态字典

**返回结构**:
```python
{
    "system_initialized": bool,         # 系统是否已初始化
    "database_connected": bool,         # 数据库是否连接
    "workflow_status": {...},           # 工作流程状态
    "settings": {                       # 系统设置
        "database": str,
        "openai_model": str,
        "security_enabled": bool
    }
}
```

## 智能体API

### BaseAgent

所有智能体的基类。

#### 方法

##### `async process_message(message: AgentMessage) -> AgentMessage`

处理消息的抽象方法，由子类实现。

**参数**:
- `message` (AgentMessage): 输入消息

**返回值**:
- `AgentMessage`: 处理后的消息

### AgentMessage

智能体间通信的消息类。

#### 属性

```python
class AgentMessage:
    type: MessageType           # 消息类型
    content: Any               # 消息内容
    metadata: Optional[Dict]   # 元数据
    sender: Optional[str]      # 发送者
    timestamp: Optional[str]   # 时间戳
```

#### 方法

##### `to_dict() -> Dict[str, Any]`

转换为字典格式。

##### `from_dict(data: Dict[str, Any]) -> AgentMessage`

从字典创建消息对象。

### MessageType

消息类型枚举。

```python
class MessageType(Enum):
    QUERY = "query"             # 查询消息
    SQL = "sql"                 # SQL消息
    RESULT = "result"           # 结果消息
    ERROR = "error"             # 错误消息
    VALIDATION = "validation"   # 验证消息
    FORMAT = "format"           # 格式化消息
```

## 数据库API

### DatabaseManager

数据库管理器，提供数据库操作接口。

#### 方法

##### `async execute_query(query: str, params: Optional[Tuple] = None, fetch_results: bool = True) -> Optional[List[Dict[str, Any]]]`

执行SQL查询。

**参数**:
- `query` (str): SQL查询语句
- `params` (Optional[Tuple]): 查询参数
- `fetch_results` (bool): 是否获取结果

**返回值**:
- `Optional[List[Dict[str, Any]]]`: 查询结果或None

##### `async execute_query_to_dataframe(query: str, params: Optional[Tuple] = None) -> pd.DataFrame`

执行查询并返回DataFrame。

##### `async test_connection() -> bool`

测试数据库连接。

##### `async get_table_schema(table_name: str) -> List[Dict[str, Any]]`

获取表结构信息。

##### `async get_all_tables() -> List[str]`

获取所有表名。

## 安全API

### SQLSecurityValidator

SQL安全验证器。

#### 方法

##### `validate_sql_operation(sql: str) -> Tuple[bool, str]`

验证SQL操作是否被允许。

##### `check_sql_injection(sql: str) -> Tuple[bool, List[str]]`

检查SQL注入攻击。

##### `sanitize_sql(sql: str) -> str`

清理SQL语句。

##### `validate_query_complexity(sql: str) -> Tuple[bool, str]`

验证查询复杂度。

## 错误处理API

### SystemError

系统自定义异常基类。

```python
class SystemError(Exception):
    def __init__(
        self, 
        message: str, 
        error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    )
```

### ErrorType

错误类型枚举。

```python
class ErrorType(Enum):
    DATABASE_ERROR = "database_error"
    SQL_SYNTAX_ERROR = "sql_syntax_error"
    SECURITY_ERROR = "security_error"
    VALIDATION_ERROR = "validation_error"
    NETWORK_ERROR = "network_error"
    CONFIGURATION_ERROR = "configuration_error"
    AGENT_ERROR = "agent_error"
    UNKNOWN_ERROR = "unknown_error"
```

### ErrorHandler

错误处理器。

#### 方法

##### `handle_exception(exception: Exception, context: str = "", additional_info: Optional[Dict[str, Any]] = None) -> SystemError`

处理异常并转换为系统错误。

##### `create_error_response(error: SystemError) -> Dict[str, Any]`

创建标准错误响应。

## 配置API

### AppSettings

应用配置类。

```python
class AppSettings:
    database: DatabaseSettings
    openai: OpenAISettings
    security: SecuritySettings
    logging: LogSettings
    autogen: AutoGenSettings
```

### 配置示例

```python
from config.settings import settings

# 访问数据库配置
db_host = settings.database.host
db_port = settings.database.port

# 访问OpenAI配置
api_key = settings.openai.api_key
model = settings.openai.model

# 访问安全配置
max_timeout = settings.security.max_query_timeout
allowed_ops = settings.security.allowed_operations
```

## 使用示例

### 完整示例

```python
import asyncio
from main import TextToSQLSystem

async def main():
    # 创建系统实例
    system = TextToSQLSystem()
    
    # 初始化系统
    if not await system.initialize():
        print("系统初始化失败")
        return
    
    # 处理单个查询
    result = await system.process_query("查询所有用户信息")
    if result["success"]:
        print("查询成功:")
        print(result["final_result"]["formatted_output"])
    else:
        print(f"查询失败: {result['error']}")
    
    # 批量处理查询
    queries = [
        "统计用户总数",
        "查询最近的10个订单",
        "计算平均订单金额"
    ]
    
    results = await system.process_batch_queries(queries)
    for i, result in enumerate(results):
        print(f"\n查询 {i+1}: {queries[i]}")
        if result["success"]:
            summary = result["final_result"]["summary"]
            print(f"结果: {summary.get('message', '处理完成')}")
        else:
            print(f"失败: {result['error']}")
    
    # 获取系统状态
    status = await system.get_system_status()
    print(f"\n系统状态:")
    print(f"  数据库连接: {'✅' if status['database_connected'] else '❌'}")
    print(f"  数据库名称: {status['settings']['database']}")

if __name__ == "__main__":
    asyncio.run(main())
```

### 错误处理示例

```python
from utils.error_handler import error_handler, SystemError, ErrorType

@error_handler("处理用户查询")
async def process_user_query(query: str):
    # 业务逻辑
    if not query.strip():
        raise ValidationError("查询不能为空", field="query", value=query)
    
    # 处理查询...
    return result

try:
    result = await process_user_query("查询用户")
except SystemError as e:
    print(f"系统错误: {e.message}")
    print(f"错误类型: {e.error_type.value}")
    print(f"建议: {e.details.get('suggestions', [])}")
```

## 注意事项

1. **异步操作**: 所有主要API都是异步的，需要使用`await`关键字
2. **错误处理**: 建议使用try-catch块处理可能的异常
3. **资源管理**: 系统会自动管理数据库连接，无需手动关闭
4. **配置管理**: 配置通过环境变量和.env文件管理
5. **日志记录**: 系统自动记录操作日志，可通过配置调整日志级别

## 更多信息

- [技术架构文档](./architecture.md)
- [配置说明](./configuration.md)
- [故障排除](./troubleshooting.md)
